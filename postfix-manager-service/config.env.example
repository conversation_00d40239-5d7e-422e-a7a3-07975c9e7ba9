# Postfix Manager Service Configuration
# Copy to /etc/postfix-manager/config.env or set as environment variables

# Service settings
PORT=3001
LOG_LEVEL=info

# File paths (adjust for your system)
VIRTUAL_FILE=/etc/postfix/virtual
MAIN_CF_FILE=/etc/postfix/main.cf
ALIASES_FILE=/etc/aliases

# Email processing script path - updated for Docker deployment
PROCESS_EMAIL_SCRIPT=/opt/eu-email-webhook/scripts/production/process-email.js

# Security settings
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,https://mw.xadi.eu

# Postfix commands (adjust if different paths)
POSTFIX_CMD=/usr/sbin/postfix
POSTMAP_CMD=/usr/sbin/postmap
SYSTEMCTL_CMD=/usr/bin/systemctl