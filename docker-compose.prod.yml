services:
  app:
    image: ghcr.io/xadi-hq/eu-email-webhook:${IMAGE_TAG:-latest}
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=${DB_HOST:-postgres}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=${DB_NAME}
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL:-redis://redis:6379}
      - POSTFIX_MANAGER_URL=http://postfix-manager:3001
      - JWT_SECRET=${JWT_SECRET}
      - DNS_VERIFICATION_TIMEOUT_MS=${DNS_VERIFICATION_TIMEOUT_MS:-5000}
      - DNS_VERIFICATION_CACHE_TTL_MS=${DNS_VERIFICATION_CACHE_TTL_MS:-300000}
      - DNS_VERIFICATION_RETRY_ATTEMPTS=${DNS_VERIFICATION_RETRY_ATTEMPTS:-3}
      - EMAIL_RETENTION_DAYS=${EMAIL_RETENTION_DAYS:-30}
      - LOG_RETENTION_DAYS=${LOG_RETENTION_DAYS:-90}
      - MAX_EMAIL_SIZE_MB=${MAX_EMAIL_SIZE_MB:-25}
      - DEFAULT_MONTHLY_EMAIL_LIMIT=${DEFAULT_MONTHLY_EMAIL_LIMIT:-50}
      - FREE_PLAN_EMAIL_LIMIT=${FREE_PLAN_EMAIL_LIMIT:-50}
      - PRO_PLAN_EMAIL_LIMIT=${PRO_PLAN_EMAIL_LIMIT:-1000}
      - ENTERPRISE_PLAN_EMAIL_LIMIT=${ENTERPRISE_PLAN_EMAIL_LIMIT:-10000}
      - WEBHOOK_TIMEOUT_MS=${WEBHOOK_TIMEOUT_MS:-30000}
      - WEBHOOK_RETRY_ATTEMPTS=${WEBHOOK_RETRY_ATTEMPTS:-3}
      - PROCESS_EMAIL_SCRIPT=/opt/eu-email-webhook/scripts/production/process-email.js
      - USER_JWT_SECRET=${USER_JWT_SECRET:-your-very-strong-and-secret-key-for-user-jwt}
      - USER_JWT_EXPIRES_IN=${USER_JWT_EXPIRES_IN:-7d}
      - ADMIN_USERNAME=admin
      - ADMIN_PASSWORD=changeme
      - ADMIN_JWT_SECRET=your-very-strong-and-secret-key-for-admin-jwt
      - ADMIN_JWT_EXPIRES_IN=1h
      - MOLLIE_API_KEY=${MOLLIE_API_KEY}
      - MOLLIE_WEBHOOK_SECRET=${MOLLIE_WEBHOOK_SECRET}
      - MOLLIE_WEBHOOK_URL=${MOLLIE_WEBHOOK_URL}
      - MOLLIE_TEST_MODE=${MOLLIE_TEST_MODE:-false}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      postfix-manager:
        condition: service_healthy
    restart: unless-stopped
    volumes:
      - eu_email_scripts:/opt/eu-email-webhook/scripts:ro
    command: sh -c "dockerize -wait tcp://postgres:5432 -wait tcp://redis:6379 -wait tcp://postfix-manager:3001 -timeout 60s && npx prisma migrate deploy && npm start"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # 🎯 DRAMATICALLY SIMPLIFIED POSTFIX MANAGER (4 mounts vs 20+)
  postfix-manager:
    image: ghcr.io/xadi-hq/eu-email-webhook/postfix-manager:${POSTFIX_TAG:-latest}
    build:
      context: .
      dockerfile: ./postfix-manager-service/Dockerfile
    ports:
      - "3001:3001"
    environment:
      - PORT=3001
      - LOG_LEVEL=info
      - PROCESS_EMAIL_SCRIPT=/opt/eu-email-webhook/scripts/production/process-email.js
      - ALLOWED_ORIGINS=https://mw.xadi.eu
      - SQLITE_DB_PATH=/opt/eu-email-webhook/data/postfix.db
      - VIRTUAL_DOMAINS_CF=/opt/eu-email-webhook/data/virtual_domains.cf
      - VIRTUAL_ALIASES_CF=/opt/eu-email-webhook/data/virtual_aliases.cf
    volumes:
      - /etc/postfix/main.cf:/etc/postfix/main.cf:rw
      - /etc/aliases:/etc/aliases:rw
      - /opt/eu-email-webhook/data:/opt/eu-email-webhook/data:rw
      - eu_email_scripts:/opt/eu-email-webhook/scripts:ro
    restart: unless-stopped
    privileged: true  # Still needed for host Postfix management
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.25'

  postgres:
    image: postgres:17-alpine
    environment:
      - POSTGRES_USER=${DB_USER:-postgres}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_DB=${DB_NAME:-eu_email_webhook}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-postgres} -d ${DB_NAME:-eu_email_webhook}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    ports:
      - "5432:5432"
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
    shm_size: 64mb

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 5s
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 128mb --maxmemory-policy allkeys-lru
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.1'

  # 🎯 SCRIPT INITIALIZATION SERVICE
  script-init:
    image: ghcr.io/xadi-hq/eu-email-webhook:${IMAGE_TAG:-latest}
    volumes:
      - eu_email_scripts:/opt/eu-email-webhook/scripts:rw
    command: sh -c "cp -r /app/scripts/* /opt/eu-email-webhook/scripts/ && chmod +x /opt/eu-email-webhook/scripts/*.js"
    restart: "no"
    profiles:
      - init

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  eu_email_scripts:
    driver: local
