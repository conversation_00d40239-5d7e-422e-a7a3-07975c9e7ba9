#!/usr/bin/env node
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function runMigration() {
  console.log('🔄 Running Phase 1 migration...\n');

  try {
    const tableCheck = await prisma.$queryRaw`
      SELECT table_name FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_name IN ('users', 'webhooks');
    `;

    if (Array.isArray(tableCheck) && tableCheck.length > 0) {
      console.log('⚠️  Migration already applied. Use --force to override.\n');
      return;
    }

    console.log('1️⃣ Creating users table...');
    await prisma.$executeRaw`
      CREATE TABLE "users" (
          "id" TEXT NOT NULL,
          "email" TEXT NOT NULL,
          "password" TEXT NOT NULL,
          "name" TEXT,
          "verified" BOOLEAN NOT NULL DEFAULT false,
          "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          CONSTRAINT "users_pkey" PRIMARY KEY ("id")
      );
    `;

    console.log('2️⃣ Creating webhooks table...');
    await prisma.$executeRaw`
      CREATE TABLE "webhooks" (
          "id" TEXT NOT NULL,
          "name" TEXT NOT NULL,
          "url" TEXT NOT NULL,
          "description" TEXT,
          "active" BOOLEAN NOT NULL DEFAULT true,
          "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "userId" TEXT NOT NULL,
          CONSTRAINT "webhooks_pkey" PRIMARY KEY ("id")
      );
    `;

    console.log('3️⃣ Adding columns to domains table...');
    await prisma.$executeRaw`ALTER TABLE "domains" ADD COLUMN "userId" TEXT;`;
    await prisma.$executeRaw`ALTER TABLE "domains" ADD COLUMN "webhookId" TEXT;`;

    console.log('4️⃣ Adding webhook column to aliases table...');
    await prisma.$executeRaw`ALTER TABLE "aliases" ADD COLUMN "webhookId" TEXT;`;
    await prisma.$executeRaw`ALTER TABLE "aliases" DROP COLUMN "webhookUrl";`;

    console.log('5️⃣ Creating indexes...');
    await prisma.$executeRaw`CREATE UNIQUE INDEX "users_email_key" ON "users"("email");`;

    console.log('6️⃣ Adding foreign key constraints...');
    await prisma.$executeRaw`
      ALTER TABLE "webhooks" ADD CONSTRAINT "webhooks_userId_fkey" 
      FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
    `;
    
    await prisma.$executeRaw`
      ALTER TABLE "domains" ADD CONSTRAINT "domains_userId_fkey" 
      FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
    `;
    
    await prisma.$executeRaw`
      ALTER TABLE "domains" ADD CONSTRAINT "domains_webhookId_fkey" 
      FOREIGN KEY ("webhookId") REFERENCES "webhooks"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
    `;

    await prisma.$executeRaw`
      ALTER TABLE "aliases" ADD CONSTRAINT "aliases_webhookId_fkey" 
      FOREIGN KEY ("webhookId") REFERENCES "webhooks"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
    `;

    console.log('7️⃣ Creating default admin user and webhook...');
    
    const defaultUserId = 'cm4q4admin000000000000000';
    await prisma.$executeRaw`
      INSERT INTO "users" ("id", "email", "password", "name", "verified")
      VALUES (${defaultUserId}, 'admin@localhost', '$2b$10$defaulthash', 'System Admin', true)
      ON CONFLICT ("id") DO NOTHING;
    `;

    const defaultWebhookId = 'cm4q4webhook00000000000000';
    await prisma.$executeRaw`
      INSERT INTO "webhooks" ("id", "name", "url", "description", "userId")
      VALUES (${defaultWebhookId}, 'Default Webhook', 'https://webhook.example.com/default', 
              'Default webhook for existing domains', ${defaultUserId})
      ON CONFLICT ("id") DO NOTHING;
    `;

    console.log('8️⃣ Migrating existing data...');
    const existingDomains = await prisma.$queryRaw`
      SELECT id, "webhookUrl" FROM "domains" WHERE "userId" IS NULL;
    `;
    
    if (Array.isArray(existingDomains) && existingDomains.length > 0) {
      for (const domain of existingDomains as any[]) {
        const webhookId = `webhook_${domain.id}`;
        
        await prisma.$executeRaw`
          INSERT INTO "webhooks" ("id", "name", "url", "userId")
          VALUES (${webhookId}, ${`Webhook for domain ${domain.id}`}, 
                  ${domain.webhookUrl}, ${defaultUserId});
        `;
        
        await prisma.$executeRaw`
          UPDATE "domains" SET "userId" = ${defaultUserId}, "webhookId" = ${webhookId}
          WHERE "id" = ${domain.id};
        `;
      }
    }

    const existingAliases = await prisma.$queryRaw`
      SELECT id, "webhookUrl", "domainId" FROM "aliases" WHERE "webhookId" IS NULL;
    `;
    
    if (Array.isArray(existingAliases) && existingAliases.length > 0) {
      for (const alias of existingAliases as any[]) {
        const webhookId = `alias_webhook_${alias.id}`;
        
        await prisma.$executeRaw`
          INSERT INTO "webhooks" ("id", "name", "url", "userId")
          VALUES (${webhookId}, ${`Webhook for alias ${alias.id}`}, 
                  ${alias.webhookUrl}, ${defaultUserId});
        `;
        
        await prisma.$executeRaw`
          UPDATE "aliases" SET "webhookId" = ${webhookId} WHERE "id" = ${alias.id};
        `;
      }
    }

    console.log('9️⃣ Making foreign keys required...');
    await prisma.$executeRaw`ALTER TABLE "domains" ALTER COLUMN "userId" SET NOT NULL;`;
    await prisma.$executeRaw`ALTER TABLE "domains" ALTER COLUMN "webhookId" SET NOT NULL;`;
    await prisma.$executeRaw`ALTER TABLE "aliases" ALTER COLUMN "webhookId" SET NOT NULL;`;

    console.log('✅ Migration completed successfully!\n');
    console.log('Next steps:');
    console.log('1. Run: node scripts/test-user-relationships.js');
    console.log('2. Begin Phase 2: User Authentication\n');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

runMigration().catch(console.error);
