# EU Email Webhook Service - Business Context

## Project Overview

EU Email Webhook Service is a GDPR-compliant email-to-webhook forwarder that converts incoming emails into HTTP POST requests. Built for EU data sovereignty requirements, it enables applications to receive email notifications without storing email content long-term.

**Current Status:** ✅ **PRODUCTION DEPLOYED** - Fully operational service with automated CI/CD pipeline on Hetzner server (miwh.xadi.eu).

## Goals

1. **Email Processing**: Receive emails via SMTP and convert to webhook payloads
2. **GDPR Compliance**: EU-hosted, automatic data expiration, audit logging
3. **Multi-tenant**: Support multiple domains with custom webhook endpoints
4. **Production Ready**: Scalable, monitored, secure deployment infrastructure
5. **Developer Friendly**: Clear API, documentation, integration examples

## Architecture Overview

### Core Technology Stack
- **Backend**: Node.js + Fastify + TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Queue**: Redis with Bull queue system
- **Email Server**: Postfix (host-level) + Go-based postfix-manager service
- **Infrastructure**: Docker Compose V2 + Ploi hosting
- **Deployment**: GitHub Actions CI/CD → Hetzner server (**************)

### Service Architecture
```
External Email → Host Postfix (port 25) → Email Bridge → Docker App → Webhook Delivery
External HTTPS → Host Nginx (SSL) → Docker App API
```

**Key Design Decision**: Hybrid architecture where Docker handles application services while host system handles email infrastructure and public access.

## Current Implementation Status

### ✅ Completed Features
1. **Email Processing Pipeline**
   - SMTP email reception via Postfix
   - Email parsing with mailparser
   - Webhook delivery with retry logic
   - Queue-based processing with Bull/Redis

2. **Domain Management API**
   - Domain registration and configuration
   - DNS ownership verification (TXT records)
   - MX record validation
   - Domain-specific webhook routing

3. **Production Infrastructure** ✅ **DEPLOYED**
   - Docker containerization for all services
   - GitHub Actions CI/CD pipeline (automated deployment)
   - Production deployment on Hetzner server
   - SSL certificate automation (via Ploi/Let's Encrypt)
   - Health monitoring and logging
   - Automatic port conflict resolution
   - Service cleanup and dependency management

4. **GDPR Compliance**
   - Automatic email data expiration (30 days)
   - Audit logging for all operations
   - EU-only server deployment (Hetzner Germany)
   - Data deletion on domain removal

### 🚧 In Development (See TASK_LIST.md)
1. **Customer Frontend Application** (Vue 3 + Tailwind CSS)
2. **API Authentication System** (JWT + API keys)
3. **OpenAPI Documentation Generation**
4. **Security & Compliance Hardening**
5. **Customer Analytics Dashboard**
6. **Monitoring & Alerting Setup**
7. **Comprehensive Test Suite**

## User Stories

### Primary Users
1. **SaaS Developers**: Need to receive customer emails in their applications
2. **API Integrators**: Want webhook-based email notifications
3. **EU Companies**: Require GDPR-compliant email processing

### Core User Flows
1. **Domain Setup**: Register domain → Configure DNS → Verify ownership → Receive emails
2. **Email Processing**: Email sent to domain → Parsed → Delivered as webhook → Tracked
3. **Monitoring**: View delivery stats → Check failed deliveries → Retry failed webhooks

## Functional Requirements

### Email Processing
1. System MUST accept emails via SMTP on port 25 ✅
2. System MUST parse email content (headers, body, attachments) ✅
3. System MUST deliver webhook payloads within 30 seconds ✅
4. System MUST retry failed webhook deliveries (max 3 attempts) ✅
5. System MUST support HTML and plain text emails ✅
6. System MUST handle email attachments (base64 encoded in webhook) ✅

### Domain Management
7. System MUST allow domain registration via API ✅
8. System MUST verify domain ownership via DNS TXT records ✅
9. System MUST validate MX record configuration ✅
10. System MUST support multiple domains per customer ✅
11. System MUST allow domain-specific webhook URLs ✅

### GDPR Compliance
12. System MUST automatically delete email data after 30 days ✅
13. System MUST log all data processing activities ✅
14. System MUST provide data export functionality ✅
15. System MUST allow complete data deletion on request ✅

### API & Integration
16. System MUST provide REST API for all operations ✅
17. System MUST include comprehensive API documentation 🚧
18. System MUST support API authentication (JWT/API keys) 🚧
19. System MUST provide webhook delivery statistics ✅
20. System MUST include rate limiting protection 🚧

## Non-Goals (Out of Scope)
- Email composition or sending capabilities
- Long-term email storage or archival
- Email client interface (webmail)
- Integration with email marketing platforms
- Support for non-EU server deployment
- Real-time email encryption/decryption

## Technical Architecture

### Database Schema (Prisma)
```typescript
- Domain: domain configuration and verification
- Alias: email aliases within domains  
- Email: temporary email metadata for processing
- AuditLog: GDPR compliance logging
```

### API Endpoints
```
GET  /health - Service health check ✅
POST /api/domains - Register domain ✅
GET  /api/domains/:domain - Get domain status ✅
POST /api/domains/:domain/verify - Verify ownership ✅
GET  /api/webhook/stats - Delivery statistics ✅
POST /api/webhook/retry/:jobId - Retry failed delivery ✅
```

### Production Environment
```
Production Server (Hetzner): **************
├── Host System (ploi user):
│   ├── Nginx (SSL termination, reverse proxy)
│   ├── Postfix (email reception on port 25)
│   ├── Docker Compose V2
│   └── UFW (firewall: 22, 25, 80, 443)
└── Docker Containers (/home/<USER>/eu-email-webhook):
    ├── app: Node.js application (port 3000)
    ├── postgres: Database
    ├── redis: Queue system
    └── postfix-manager: Domain config service (port 3001)
```

## Integration Examples

### Customer Onboarding Flow
```bash
# 1. Register domain
curl -X POST https://miwh.xadi.eu/api/config/domains \
  -H "Content-Type: application/json" \
  -d '{"domain": "example.com", "webhookUrl": "https://app.com/webhook"}'

# 2. Configure DNS records
# MX: @ -> mw.xadi.eu (priority 10)
# TXT: @ -> verify-mw=example.com

# 3. Verify domain
curl -X POST https://miwh.xadi.eu/api/config/domains/example.com/verify

# 4. Receive webhooks
# <NAME_EMAIL> -> POST to https://app.com/webhook
```

### Webhook Payload Format
```json
{
  "messageId": "unique-id",
  "domain": "example.com",
  "fromAddress": "<EMAIL>",
  "toAddresses": ["<EMAIL>"],
  "subject": "Email subject",
  "body": "Email content",
  "htmlBody": "<html>...</html>",
  "attachments": [],
  "timestamp": "2025-05-26T12:00:00Z",
  "retryCount": 0
}
```

## Success Metrics
1. **Email Processing**: 99.9% successful webhook delivery rate ✅
2. **Performance**: <30 second email-to-webhook latency ✅
3. **Reliability**: 99.5% uptime for API endpoints ✅
4. **GDPR**: 100% compliance with data retention policies ✅
5. **Developer Experience**: <5 minute domain setup time ✅

## Production URLs & Access
- **Primary Service**: https://miwh.xadi.eu
- **Health Check**: https://miwh.xadi.eu/health ✅
- **Server**: ************** (Hetzner, Germany)
- **User**: ploi
- **App Directory**: /home/<USER>/eu-email-webhook

## Current Deployment Status
- **Last Deployment**: ✅ Successful
- **Main Application**: ✅ Healthy (returning JSON health status)
- **Database**: ✅ Connected
- **Postfix Manager**: ✅ Operational
- **SSL Certificate**: ✅ Valid
- **Email Processing**: ✅ Ready
- **Webhook Delivery**: ✅ Functional

---

**Note**: This service is **PRODUCTION DEPLOYED and OPERATIONAL**. The deployment pipeline is working, all core services are healthy, and the system is processing emails to webhooks successfully. Current development focus is on customer-facing features, API authentication, and operational improvements.
