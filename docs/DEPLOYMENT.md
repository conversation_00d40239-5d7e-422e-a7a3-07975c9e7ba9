# Deployment Guide

## Overview

The EU Email Webhook service uses a **hybrid architecture** with automated application deployment and manual server provisioning. This guide covers the application deployment process - for server setup, see `SERVER_PROVISIONING.md`.

## Architecture

### Hybrid Deployment Model
```
┌─────────────────── HOST SYSTEM ───────────────────┐
│                                                   │
│  📧 Postfix (port 25) ── receives emails         │
│  🌐 Nginx (ports 80/443) ── SSL + reverse proxy  │
│  🔥 UFW Firewall ── network security             │
│  🐳 Docker Engine ── container runtime           │
│                                                   │
│  ┌──────────── DOCKER CONTAINERS ─────────────┐   │
│  │                                            │   │
│  │  🟢 Node.js App (port 3000)               │   │
│  │  🔴 Redis (port 6379)                     │   │
│  │  🟡 PostgreSQL (port 5432)                │   │
│  │  🟣 Postfix Manager (port 3001)           │   │
│  │                                            │   │
│  └────────────────────────────────────────────┘   │
│                                                   │
└───────────────────────────────────────────────────┘
```

### Separation of Concerns
- **Application Layer**: Automated via Docker and GitHub Actions
- **Infrastructure Layer**: Manual setup via documentation
- **Email Layer**: Host-based Postfix integration
- **Web Layer**: Nginx reverse proxy on host

## Automated Deployment Pipeline

### GitHub Actions CI/CD

The deployment pipeline runs automatically on pushes to main branch:

1. **Test Phase**:
   - Lint and type checking
   - Build validation

2. **Build Phase**:
   - Multi-stage Docker builds
   - Push to GitHub Container Registry
   - Builds both main app and postfix-manager

3. **Deploy Phase**:
   - SSH to production server
   - Pull latest images
   - Update services with zero downtime
   - Health check validation

### Container Registry Strategy

**Where Images Live:**
- **Development**: Built locally
- **CI/CD**: GitHub Container Registry (GHCR)
- **Production**: Pulls from GHCR

**Image Tagging:**
```bash
# Latest (main branch)
ghcr.io/xadi-hq/eu-email-webhook:latest

# Version tags (releases)  
ghcr.io/xadi-hq/eu-email-webhook:v1.2.3

# Commit SHA (for debugging)
ghcr.io/xadi-hq/eu-email-webhook:sha-abc123
```

## Manual Deployment (Production)

### Prerequisites
- Server provisioned (see `SERVER_PROVISIONING.md`)
- Environment file `.env.prod` configured
- GitHub Container Registry access

### Standard Deployment
```bash
cd /opt/eu-email-webhook

# Deploy with proper environment
docker compose -f docker-compose.prod.yml --env-file .env.prod pull
docker compose -f docker-compose.prod.yml --env-file .env.prod up -d --force-recreate

# Run database migrations
docker compose -f docker-compose.prod.yml --env-file .env.prod exec app npx prisma migrate deploy

# Verify deployment
./deploy/validate-deployment.sh
```

### Environment Variables

Critical environment variables in `.env.prod`:
```env
NODE_ENV=production
DATABASE_URL=************************************/dbname
REDIS_URL=redis://redis:6379
JWT_SECRET=your-secure-jwt-secret-64-chars-minimum
WEBHOOK_TIMEOUT_MS=30000
WEBHOOK_RETRY_ATTEMPTS=3
EMAIL_RETENTION_DAYS=30
```

## Image-Based Deployment (Best Practice)

### Industry Standard Approach
✅ **Best Practice: Image-Based Deployment**
```bash
# DO THIS
docker pull ghcr.io/xadi-hq/eu-email-webhook:v1.2.3
docker compose up -d --force-recreate
```

❌ **Anti-Pattern: Git Pull in Production**
```bash
# DON'T DO THIS
git pull origin main
npm install
npm run build
```

**Benefits of Image-Based:**
- ✅ Same artifact tested in CI
- ✅ Atomic deployments  
- ✅ Instant rollbacks
- ✅ Environment consistency
- ✅ Immutable infrastructure

## Data Flow

### Email Processing Flow
```
1. External Email
   ↓
2. Host Postfix (port 25)
   ↓
3. SQLite Database Query
   ↓
4. process-email.js Script
   ↓
5. Docker App (port 3000)
   ↓
6. Redis Queue
   ↓
7. Webhook Delivery
```

### API Request Flow
```
1. External HTTP/HTTPS Request
   ↓
2. Nginx Reverse Proxy
   ↓
3. Docker App (port 3000)
   ↓
4. PostgreSQL Database
   ↓
5. API Response
```

### Domain Management Flow
```
1. API Request (/api/domains)
   ↓
2. Node.js App (port 3000)
   ↓
3. Postfix Manager (port 3001)
   ↓
4. SQLite Database Update
   ↓
5. Postfix Configuration Update
```

## Health Monitoring

### Automated Health Checks
```bash
# Application health
curl https://miwh.xadi.eu/health

# Internal services
curl http://localhost:3000/health
curl http://localhost:3001/health
```

### Container Status
```bash
# Check all containers
docker compose -f docker-compose.prod.yml --env-file .env.prod ps

# View logs
docker compose -f docker-compose.prod.yml --env-file .env.prod logs -f app
```

## Rollback Procedures

### Quick Rollback
```bash
# Rollback to previous version
docker pull ghcr.io/xadi-hq/eu-email-webhook:v1.2.2
docker compose -f docker-compose.prod.yml --env-file .env.prod up -d --force-recreate
```

### Emergency Restart
```bash
# Complete service restart
docker compose -f docker-compose.prod.yml --env-file .env.prod down --remove-orphans
docker compose -f docker-compose.prod.yml --env-file .env.prod up -d

# Wait and verify
sleep 60
curl https://miwh.xadi.eu/health
```

## Multi-Environment Strategy

### Environment Promotion
```
Development → Staging → Production
   ↓            ↓         ↓
Local build → CI build → Same image
```

### Configuration Management
- **Images**: Same across all environments
- **Config**: Environment-specific via `.env` files
- **Secrets**: Environment variables (not in images)

## Deployment Validation

### Post-Deployment Checklist
```bash
# 1. Verify environment consistency
docker compose -f docker-compose.prod.yml --env-file .env.prod config | grep DATABASE_URL

# 2. Verify all containers healthy
docker compose -f docker-compose.prod.yml --env-file .env.prod ps

# 3. Verify services respond
curl -f http://localhost:3000/health
curl -f http://localhost:3001/health

# 4. Test external access
curl -f https://miwh.xadi.eu/health

# 5. Run comprehensive validation
./deploy/validate-deployment.sh
```

## Security Considerations

### Container Security
- Non-root execution where possible
- Minimal base images (alpine variants)
- Multi-stage builds to reduce attack surface
- Regular base image updates via CI/CD

### Network Security
- Internal Docker network for service communication
- Only necessary ports exposed to host
- SSL termination handled by host Nginx
- Firewall configured (UFW: 22, 25, 80, 443)

### Secrets Management
- Environment variables for sensitive configuration
- GitHub Secrets for CI/CD credentials
- No secrets in Dockerfiles or compose files

## Performance Optimization

### Build Optimization
- Multi-stage builds reduce image size
- Docker layer caching in GitHub Actions
- Efficient dependency installation
- Build cache reuse between deployments

### Runtime Optimization
- Resource limits prevent resource exhaustion
- Health checks ensure service reliability
- Volume mounting for persistent data
- Graceful shutdown handling

## Troubleshooting Common Issues

### Container Issues
```bash
# Check container logs
docker compose -f docker-compose.prod.yml --env-file .env.prod logs <service-name>

# Restart specific service
docker compose -f docker-compose.prod.yml --env-file .env.prod up -d <service-name>
```

### Environment Variable Issues
- Always use `--env-file .env.prod` in EVERY Docker Compose command
- Verify environment loading: `docker compose config | grep DATABASE_URL`

### Database Issues
```bash
# Check database connectivity
docker compose -f docker-compose.prod.yml --env-file .env.prod exec app npx prisma db pull

# Run migrations manually
docker compose -f docker-compose.prod.yml --env-file .env.prod exec app npx prisma migrate deploy
```

## Critical Success Factors

### The Golden Rules
1. **🎯 ALWAYS use `--env-file .env.prod` in every Docker Compose command**
2. **📁 Use image-based deployment, not git pull in production**
3. **🔧 Test internal connectivity before external access**
4. **🚀 Use consistent command patterns**
5. **📝 Validate deployment after every update**

---

**Note**: This guide covers application deployment. For initial server setup, DNS configuration, and SSL certificates, see `SERVER_PROVISIONING.md`. For troubleshooting deployment issues, see `OPERATIONS.md`.
