# Postfix SQLite Query Configuration for Virtual Aliases
# This file defines how Postfix queries for specific email aliases within domains

# Query for virtual alias lookup
# Used by Postfix to find the destination for specific email addresses
query = SELECT destination FROM virtual_aliases WHERE email='%s' AND active=1

# Fallback query for catch-all domain handling
# If no specific alias found, check if domain has catch-all
# query = SELECT destination FROM virtual_domains WHERE domain='%d' AND active=1

# Database connection settings
hosts = /opt/eu-email-webhook/data/postfix.db
dbpath = /opt/eu-email-webhook/data/postfix.db

# Optional: Connection settings
# result_format = %s
# expansion_limit = 0

# Security settings  
# domain = 
# hosts = localhost
# user = 
# password = 

# Debugging
# debuglevel = 0
