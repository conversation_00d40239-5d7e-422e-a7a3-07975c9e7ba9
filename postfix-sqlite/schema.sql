-- Postfix SQLite Schema for Virtual Domains and Aliases
-- This replaces the file-based /etc/postfix/virtual approach

-- Virtual domains table
CREATE TABLE IF NOT EXISTS virtual_domains (
    domain VARCHAR(255) PRIMARY KEY,
    destination VARCHAR(255) NOT NULL DEFAULT 'process-email',
    active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index for performance
CREATE INDEX IF NOT EXISTS idx_virtual_domains_active ON virtual_domains(domain, active);

-- Virtual aliases table (for specific email addresses within domains)
CREATE TABLE IF NOT EXISTS virtual_aliases (
    email VARCHAR(255) PRIMARY KEY,
    destination VARCHAR(255) NOT NULL,
    domain VARCHAR(255),
    active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (domain) REFERENCES virtual_domains(domain) ON DELETE CASCADE
);

-- Index for performance
CREATE INDEX IF NOT EXISTS idx_virtual_aliases_active ON virtual_aliases(email, active);
CREATE INDEX IF NOT EXISTS idx_virtual_aliases_domain ON virtual_aliases(domain);

-- Trigger to update updated_at timestamp
CREATE TRIGGER IF NOT EXISTS update_virtual_domains_timestamp 
    AFTER UPDATE ON virtual_domains
BEGIN
    UPDATE virtual_domains 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE domain = NEW.domain;
END;

CREATE TRIGGER IF NOT EXISTS update_virtual_aliases_timestamp 
    AFTER UPDATE ON virtual_aliases
BEGIN
    UPDATE virtual_aliases 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE email = NEW.email;
END;
