#!/bin/bash

# Simple deployment webhook script
# This runs when GitHub sends a webhook on push to main
# Updated for /opt/eu-email-webhook path standardization

set -e

APP_DIR="/opt/eu-email-webhook"
DEPLOY_LOG="/var/log/deploy.log"

echo "$(date): Deployment webhook triggered" >> $DEPLOY_LOG

cd $APP_DIR

# Pull latest changes
git pull origin main

# Install any new dependencies
npm ci --omit=dev

# Build TypeScript
npm run build

# Restart services using Docker Compose
docker compose -f docker-compose.prod.yml down
docker compose -f docker-compose.prod.yml up -d

# Log success
echo "$(date): Deployment completed successfully" >> $DEPLOY_LOG

# Send success response
echo "Content-Type: application/json"
echo ""
echo '{"status": "deployed", "timestamp": "'$(date -Iseconds)'"}'
