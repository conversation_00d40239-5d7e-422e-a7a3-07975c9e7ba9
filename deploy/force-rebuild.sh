#!/bin/bash

# 🔧 Force Complete Container Rebuild
# This ensures we're running the latest code with debug logging

echo "🔧 Force Complete Container Rebuild"
echo "=================================="

echo "Step 1: Stop all containers"
docker compose down

echo "Step 2: Remove postfix-manager container completely"
docker compose rm -f postfix-manager

echo "Step 3: Remove postfix-manager image"
docker rmi $(docker images | grep postfix-manager | awk '{print $3}') 2>/dev/null || echo "No postfix-manager image to remove"

echo "Step 4: Clean build cache"
docker builder prune -f

echo "Step 5: Build with no cache and verbose output"
docker compose build --no-cache --progress=plain postfix-manager

echo "Step 6: Start fresh container"
docker compose up -d postfix-manager

echo "Step 7: Wait for startup"
sleep 10

echo "Step 8: Check container is running"
docker compose ps postfix-manager

echo "Step 9: Test with debug logging"
TEST_DOMAIN="fresh-rebuild-$(date +%s).example.com"
echo "Testing domain: $TEST_DOMAIN"

echo "Making API call..."
response=$(curl -s -X POST "http://localhost:3001/domains" \
    -H "Content-Type: application/json" \
    -d "{\"domain\":\"$TEST_DOMAIN\"}")

echo "API Response: $response"

echo ""
echo "Step 10: Check logs for debug output"
echo "Looking for 🔍 debug logs..."
docker logs eu-email-webhook-postfix-manager-1 | grep "🔍" || echo "❌ No debug logs found - still running old code!"

echo ""
echo "Recent container logs:"
docker logs --tail 20 eu-email-webhook-postfix-manager-1

echo ""
echo "Step 11: Check database"
sleep 2
result=$(docker exec -it eu-email-webhook-postfix-manager-1 sqlite3 /opt/eu-email-webhook/data/postfix.db "SELECT domain FROM virtual_domains WHERE domain='$TEST_DOMAIN';" 2>/dev/null | tr -d '\r')

if [ -n "$result" ]; then
    echo "✅ SUCCESS! Domain found: $result"
    echo "🎉 Debug logging is working and SQLite insertion succeeded!"
else
    echo "❌ Domain not found - need to investigate further"
fi
