# EU Email Webhook - Deployment Guide

This directory contains deployment scripts and configurations for the EU Email Webhook service.

## 🚀 Production Deployment (GitHub Actions)

The service uses a modern Docker-based deployment with **automated CI/CD pipeline via GitHub Actions**.

### Quick Start

1.  **Automated Deployment (Recommended)**
    ```bash
    # Push to main branch triggers automatic deployment
    git push origin main
    ```

2.  **Manual Server Validation**
    ```bash
    # After deployment, validate everything works
    ./deploy/validate-deployment.sh
    ```

## 📋 Current Files

This section lists the key scripts found in the `deploy/` directory.

- **`validate-deployment.sh`**: A script to perform comprehensive health checks and validation of the deployed application and its components.
- **`initialize.sh`**: A script to check the host environment for necessary dependencies (like Postfix, `postmap`, `systemctl`) and configurations before deploying the application.
- **`backup_postfix.sh`**: A script to create timestamped backups of the `/etc/postfix` directory, manage log rotation, and ensure configuration safety.
- **`README.md`**: This deployment guide.

## Initial Server Preparation

This section details the `deploy/initialize.sh` script, which is designed to prepare the host environment before the EU Email Webhook application is deployed.

### Purpose
The `deploy/initialize.sh` script checks the host system for essential dependencies and configurations. This includes verifying the presence and correct setup of:
- Postfix (Mail Transfer Agent)
- `postmap` command (for Postfix lookup tables)
- `systemctl` (for managing system services)
- Other necessary tools and directory structures.

Running this script helps ensure that the server meets all prerequisites, preventing potential deployment or runtime issues.

### Basic Usage
To execute the script, run it with superuser privileges:
```bash
sudo bash deploy/initialize.sh
```

### When to Run
It is recommended to run `initialize.sh`:
- Once before the very first deployment of the application to a new server.
- If the host environment is suspected to have changed (e.g., after OS updates or manual configuration modifications) and you want to re-verify the dependencies.

## Postfix Configuration Backup

This section describes the `deploy/backup_postfix.sh` script, used for backing up Postfix configurations.

### Purpose
The `deploy/backup_postfix.sh` script is designed to create timestamped backups of the `/etc/postfix` directory. This is crucial for safeguarding your Postfix configurations, especially when managed by tools like `postfix-manager` which might make automated changes.

### Key Features
- **Timestamped Backups**: Creates backups with a format like `postfix_config_YYYYMMDD_HHMMSS.tar.gz`.
- **Backup Location**: Stores backups in `/var/backups/postfix_config/`.
- **Logging**: Records backup activities to `/var/log/postfix_backup.log`.
- **Rotation**: Automatically keeps the last 7 backups and removes older ones to manage disk space.

### Setup Instructions

1.  **Make the script executable:**
    ```bash
    sudo chmod +x deploy/backup_postfix.sh
    ```

2.  **Copy to a standard location (optional but recommended for system-wide use):**
    ```bash
    sudo cp deploy/backup_postfix.sh /usr/local/sbin/backup_postfix.sh
    ```

3.  **Set up a cron job for daily backups:**
    Add the following line to your crontab (e.g., by running `sudo crontab -e`):
    ```cron
    5 3 * * * /usr/local/sbin/backup_postfix.sh
    ```
    This example runs the backup script daily at 3:05 AM. Adjust the timing as needed.

### Importance
Regular backups of your Postfix configuration are vital. If `postfix-manager` or any other process inadvertently misconfigures Postfix, these backups allow for quick restoration to a known good state, minimizing email service downtime.

## 🏗️ Architecture Overview

The service uses a hybrid architecture:

```
🌍 INTERNET
    ↓
📧 EMAIL (port 25) → 🖥️  HOST POSTFIX → 🔗 Bridge → 🐳 DOCKER APP  
🌐 HTTPS (port 443) → 🖥️ HOST NGINX → 🔄 Proxy → 🐳 DOCKER APP
```

**Docker Containers Handle:**
- Node.js application (API service)
- PostgreSQL database  
- Redis queue system
- Postfix-manager (domain configuration)

**Host System Handles:**
- Email reception (Postfix on port 25)
- SSL termination (Nginx with Let's Encrypt)
- Public internet access
- System services and security

## ⚙️ Environment Configuration

Production environment is managed via `.env.prod` on the server:

```env
NODE_ENV=production
DATABASE_URL=********************************************/eu_email_webhook
REDIS_URL=redis://redis:6379
JWT_SECRET=your-jwt-secret
DB_PASSWORD=your-db-password
```

## 🔧 CI/CD Pipeline

The GitHub Actions pipeline (`../.github/workflows/deploy.yml`) handles:

1.  **Test** - Linting and type checking
2.  **Build** - Docker image creation and push to GHCR  
3.  **Deploy** - Automated deployment to production server

### Required GitHub Secrets

```
PRODUCTION_HOST=your-server-ip
PRODUCTION_USER=ploi
PRODUCTION_SSH_KEY=ssh-private-key-content
GHCR_TOKEN=github-personal-access-token
```

## 🏥 Health Checks & Monitoring

### Automated Health Checks
The deployment includes comprehensive health checks:
- Main application: `https://miwh.xadi.eu/health`
- Postfix manager: `http://localhost:3001/health`
- Database connectivity verification
- SSL certificate validation

### Manual Validation
```bash
# Run comprehensive validation
./deploy/validate-deployment.sh

# Check specific endpoints
curl https://miwh.xadi.eu/health
curl http://localhost:3001/health
```

### Application Logs
```bash
# Main application logs
docker compose -f ~/eu-email-webhook/docker-compose.prod.yml logs -f app

# Postfix manager logs
docker compose -f ~/eu-email-webhook/docker-compose.prod.yml logs -f postfix-manager

# System logs  
journalctl -u nginx -f
journalctl -u postfix -f
```

## 🧪 Testing Your Deployment

### 1. Basic Health Check
```bash
curl https://miwh.xadi.eu/health
```

### 2. Add Test Domain
```bash
curl -X POST https://miwh.xadi.eu/api/config/domains \
  -H "Content-Type: application/json" \
  -d '{"domain": "test.example.com", "webhookUrl": "https://webhook.site/xxx"}'
```

### 3. Send Test Email
```bash
# Send <NAME_EMAIL> and verify webhook delivery
```

## 🔄 Updates & Maintenance

### Automatic Updates
- Push to `main` branch → automatic deployment via GitHub Actions
- Zero-downtime rolling updates
- Automatic rollback on failure

### Manual Updates (if needed)
```bash
cd ~/eu-email-webhook
git pull origin main
docker compose -f docker-compose.prod.yml pull
docker compose -f docker-compose.prod.yml up -d
```

## 🔒 Security Features

- SSL certificates managed automatically
- Nginx reverse proxy with security headers
- Firewall configured for minimal attack surface
- Container security and isolation
- Secrets management via environment variables

## 📞 Troubleshooting

### Common Issues
1.  **Deployment fails** - Check GitHub Actions logs
2.  **Health checks fail** - Run `./deploy/validate-deployment.sh`
3.  **Email not processing** - Check postfix logs: `journalctl -u postfix -f`
4.  **API not responding** - Check app logs: `docker compose logs app`

### Getting Help
1.  Check application health: `https://miwh.xadi.eu/health`
2.  Run validation script: `./deploy/validate-deployment.sh`
3.  Review deployment logs in GitHub Actions
4.  Check server resources and disk space

## 📚 Additional Documentation

- **Architecture Details**: The old `DEPLOYMENT-COMPLETE.md` might contain more info, though this README aims to be comprehensive for deployment.
- **Validation Guide**: Run `./validate-deployment.sh --help` or inspect the script for details.
- **Repository**: https://github.com/xadi-hq/eu-email-webhook
- **Production URL**: https://miwh.xadi.eu

---

**Your EU Email Webhook service is production-ready with automated deployment!** 🚀
