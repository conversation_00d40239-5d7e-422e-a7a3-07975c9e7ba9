# Multi-stage Docker build for optimal production deployment
# Stage 1: Build stage
FROM node:22-slim AS builder

WORKDIR /app

# Install necessary system dependencies for Prisma and building
RUN apt-get update && apt-get install -y \
    openssl \
    ca-certificates \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Install dockerize for better dependency management
ARG TARGETARCH
ENV DOCKERIZE_VERSION=v0.7.0
RUN wget --progress=dot:giga https://github.com/jwilder/dockerize/releases/download/${DOCKERIZE_VERSION}/dockerize-linux-${TARGETARCH:-amd64}-${DOCKERIZE_VERSION}.tar.gz \
    && tar -C /usr/local/bin -xzvf dockerize-linux-${TARGETARCH:-amd64}-${DOCKERIZE_VERSION}.tar.gz \
    && rm dockerize-linux-${TARGETARCH:-amd64}-${DOCKERIZE_VERSION}.tar.gz

# Install dependencies for building (including devDependencies)
COPY package*.json ./
RUN npm ci && npm cache clean --force

# Copy source code and build
COPY . .
RUN rm -rf dist && npm run build

# Generate Prisma client
RUN npx prisma generate

# Stage 2: Production stage  
FROM node:22-slim AS production

# Install necessary system dependencies including curl for health checks
RUN apt-get update && apt-get install -y \
    openssl \
    ca-certificates \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Install dockerize for better dependency management
ARG TARGETARCH
ENV DOCKERIZE_VERSION=v0.7.0
RUN wget --progress=dot:giga https://github.com/jwilder/dockerize/releases/download/${DOCKERIZE_VERSION}/dockerize-linux-${TARGETARCH:-amd64}-${DOCKERIZE_VERSION}.tar.gz \
    && tar -C /usr/local/bin -xzvf dockerize-linux-${TARGETARCH:-amd64}-${DOCKERIZE_VERSION}.tar.gz \
    && rm dockerize-linux-${TARGETARCH:-amd64}-${DOCKERIZE_VERSION}.tar.gz

WORKDIR /app

# Create non-root user
RUN groupadd -r nodejs && useradd -r -g nodejs nodejs

# Copy package files and install production dependencies only
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma
COPY --from=builder /app/prisma ./prisma

# Copy any additional necessary files
COPY --from=builder /app/package.json ./package.json

# Change ownership to nodejs user
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Default command
CMD ["npm", "start"]