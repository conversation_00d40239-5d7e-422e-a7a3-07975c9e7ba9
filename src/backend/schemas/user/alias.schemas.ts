export const aliasSchemas = {
  // Alias response schemas
  AliasResponse: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      email: { type: 'string' },
      active: { type: 'boolean' },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' },
      domain: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          domain: { type: 'string' },
          verified: { type: 'boolean' }
        }
      },
      webhook: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          url: { type: 'string' },
          verified: { type: 'boolean' },
          hasSecret: { type: 'boolean' }
        }
      }
    }
  },

  AliasDetailResponse: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      email: { type: 'string' },
      active: { type: 'boolean' },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' },
      domain: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          domain: { type: 'string' },
          verified: { type: 'boolean' }
        }
      },
      webhook: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          url: { type: 'string' },
          verified: { type: 'boolean' },
          hasSecret: { type: 'boolean' }
        }
      }
    }
  },

  // Request body schemas
  CreateAliasRequest: {
    type: 'object',
    properties: {
      email: { 
        type: 'string', 
        format: 'email', 
        description: 'Full email address (e.g., <EMAIL>)' 
      },
      domainId: { 
        type: 'string', 
        description: 'ID of the domain to create alias for' 
      },
      webhookId: { 
        type: 'string', 
        description: 'ID of the webhook to use for this alias' 
      },
      active: { type: 'boolean', default: true }
    },
    required: ['email', 'domainId', 'webhookId']
  },

  UpdateAliasRequest: {
    type: 'object',
    properties: {
      webhookId: { 
        type: 'string', 
        description: 'ID of the webhook to use for this alias' 
      },
      active: { type: 'boolean' }
    },
    minProperties: 1
  },

  // Success response schemas
  CreateAliasResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      alias: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          email: { type: 'string' },
          active: { type: 'boolean' },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' },
          domainId: { type: 'string' },
          webhookId: { type: 'string' }
        }
      }
    }
  },

  UpdateAliasResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      alias: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          email: { type: 'string' },
          active: { type: 'boolean' },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' },
          domainId: { type: 'string' },
          webhookId: { type: 'string' }
        }
      }
    }
  },

  AliasListResponse: {
    type: 'object',
    properties: {
      aliases: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            active: { type: 'boolean' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
            domain: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                domain: { type: 'string' },
                verified: { type: 'boolean' }
              }
            },
            webhook: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                url: { type: 'string' },
                verified: { type: 'boolean' },
                hasSecret: { type: 'boolean' }
              }
            }
          }
        }
      },
      total: { type: 'integer' }
    }
  },

  DomainAliasListResponse: {
    type: 'object',
    properties: {
      aliases: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            active: { type: 'boolean' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
            webhook: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                url: { type: 'string' },
                verified: { type: 'boolean' },
                hasSecret: { type: 'boolean' }
              }
            }
          }
        }
      },
      domain: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          domain: { type: 'string' },
          verified: { type: 'boolean' }
        }
      },
      total: { type: 'integer' }
    }
  },

  DeleteAliasResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      message: { type: 'string' }
    }
  },

  // Parameter schemas
  AliasIdParam: {
    type: 'object',
    properties: {
      aliasId: { type: 'string' }
    },
    required: ['aliasId']
  },

  DomainIdParam: {
    type: 'object',
    properties: {
      domainId: { type: 'string' }
    },
    required: ['domainId']
  }
};
