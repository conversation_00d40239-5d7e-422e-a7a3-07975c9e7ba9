import axios from 'axios';
import { logger } from '../utils/logger.js';
import { env } from '../config/env.js';

export class PostfixManager {
  private readonly serviceUrl = env.POSTFIX_MANAGER_URL || 'http://localhost:3001';

  /**
   * Add a domain to Postfix configuration via privileged service
   */
  async addDomain(domain: string): Promise<void> {
    try {
      logger.info({ domain, serviceUrl: this.serviceUrl }, 'Adding domain via privileged service');

      const response = await axios.post(`${this.serviceUrl}/domains`, {
        domain,
      }, {
        timeout: 10000,
      });

      if (!response.data.success) {
        throw new Error(response.data.error || 'Failed to add domain');
      }

      logger.info({ domain }, 'Domain added successfully via privileged service');
    } catch (error) {
      if (error.response) {
        logger.error({ 
          domain, 
          status: error.response.status,
          error: error.response.data,
          serviceUrl: this.serviceUrl
        }, 'Privileged service error');
        throw new Error(`Failed to add domain: ${error.response.data.error || error.response.data.message}`);
      } else {
        logger.error({ domain, error: error.message, serviceUrl: this.serviceUrl }, 'Failed to communicate with privileged service');
        throw new Error(`Failed to add domain: ${error.message}`);
      }
    }
  }

  /**
   * Remove a domain from Postfix configuration via privileged service
   */
  async removeDomain(domain: string): Promise<void> {
    try {
      logger.info({ domain }, 'Removing domain via privileged service');

      const response = await axios.delete(`${this.serviceUrl}/domains/${domain}`, {
        timeout: 10000,
      });

      if (!response.data.success) {
        throw new Error(response.data.error || 'Failed to remove domain');
      }

      logger.info({ domain }, 'Domain removed successfully via privileged service');
    } catch (error) {
      if (error.response) {
        logger.error({ 
          domain, 
          status: error.response.status,
          error: error.response.data 
        }, 'Privileged service error');
        throw new Error(`Failed to remove domain: ${error.response.data.error || error.response.data.message}`);
      } else {
        logger.error({ domain, error: error.message }, 'Failed to communicate with privileged service');
        throw new Error(`Failed to remove domain: ${error.message}`);
      }
    }
  }

  /**
   * Get list of currently configured domains via privileged service
   */
  async getConfiguredDomains(): Promise<string[]> {
    try {
      const response = await axios.get(`${this.serviceUrl}/domains`, {
        timeout: 10000,
      });

      return response.data.domains || [];
    } catch (error) {
      logger.error({ error: error.message }, 'Failed to get configured domains from privileged service');
      return [];
    }
  }

  /**
   * Test Postfix configuration via privileged service
   */
  async testConfiguration(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.serviceUrl}/status`, {
        timeout: 10000,
      });

      return response.data.config_valid === true;
    } catch (error) {
      logger.error({ error: error.message }, 'Failed to test configuration via privileged service');
      return false;
    }
  }

  /**
   * Setup initial Postfix configuration via privileged service
   */
  async setupInitialConfig(): Promise<void> {
    try {
      logger.info('Setting up initial configuration via privileged service');

      const response = await axios.post(`${this.serviceUrl}/setup`, {}, {
        timeout: 30000, // Setup might take longer
      });

      if (!response.data.success) {
        throw new Error(response.data.error || 'Setup failed');
      }

      logger.info('Initial configuration completed via privileged service');
    } catch (error) {
      if (error.response) {
        logger.error({ 
          status: error.response.status,
          error: error.response.data 
        }, 'Privileged service setup error');
        throw new Error(`Setup failed: ${error.response.data.error || error.response.data.message}`);
      } else {
        logger.error({ error: error.message }, 'Failed to communicate with privileged service for setup');
        throw new Error(`Setup failed: ${error.message}`);
      }
    }
  }

  /**
   * Get privileged service status
   */
  async getServiceStatus(): Promise<any> {
    try {
      const response = await axios.get(`${this.serviceUrl}/status`, {
        timeout: 5000,
      });

      return response.data;
    } catch (error) {
      logger.error({ error: error.message }, 'Failed to get privileged service status');
      throw new Error(`Service unavailable: ${error.message}`);
    }
  }

  /**
   * Health check for privileged service
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.serviceUrl}/health`, {
        timeout: 5000,
      });

      return response.data.status === 'ok';
    } catch (error) {
      logger.error({ error: error.message }, 'Privileged service health check failed');
      return false;
    }
  }
}
