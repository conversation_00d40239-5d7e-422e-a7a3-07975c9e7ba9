import { prisma } from '../lib/prisma.js';
import { DNSVerifier } from './dns-verifier.js';
import { logger } from '../utils/logger.js';

export class VerificationWorker {
  private dnsVerifier: DNSVerifier;
  private intervalId: NodeJS.Timeout | null = null;
  private readonly CHECK_INTERVAL_MS = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.dnsVerifier = new DNSVerifier();
  }

  start(): void {
    if (this.intervalId) {
      logger.warn('Verification worker already running');
      return;
    }

    logger.info('Starting automatic domain verification worker');
    
    // Run immediately, then on interval
    this.runVerificationCycle();
    this.intervalId = setInterval(() => {
      this.runVerificationCycle();
    }, this.CHECK_INTERVAL_MS);
  }

  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      logger.info('Verification worker stopped');
    }
  }

  private async runVerificationCycle(): Promise<void> {
    try {
      logger.debug('Starting verification cycle');

      // Find domains that need verification
      const domainsToVerify = await prisma.domain.findMany({
        where: {
          AND: [
            { active: true },
            { 
              OR: [
                { verified: false },
                { 
                  AND: [
                    { verified: true },
                    { nextVerificationCheck: { lte: new Date() } }
                  ]
                }
              ]
            },
            { nextVerificationCheck: { lte: new Date() } }
          ]
        },
        orderBy: {
          nextVerificationCheck: 'asc'
        },
        take: 10 // Process max 10 domains per cycle
      });

      if (domainsToVerify.length === 0) {
        logger.debug('No domains require verification at this time');
        return;
      }

      logger.info(`Verifying ${domainsToVerify.length} domains`);

      for (const domain of domainsToVerify) {
        await this.verifyDomain(domain);
        // Small delay between verifications to avoid overwhelming DNS servers
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      logger.debug('Verification cycle completed');

    } catch (error) {
      logger.error({ error }, 'Error in verification cycle');
    }
  }

  private async verifyDomain(domain: any): Promise<void> {
    try {
      logger.debug({ domain: domain.domain }, 'Verifying domain');

      const verificationResult = await this.dnsVerifier.verifyDomainOwnership(domain.domain);

      // Calculate next check time based on result
      const now = new Date();
      let nextCheck: Date;
      let newStatus: 'PENDING' | 'VERIFIED' | 'FAILED';

      if (verificationResult.verified) {
        // Success - check again in 24 hours
        nextCheck = new Date(now.getTime() + 24 * 60 * 60 * 1000);
        newStatus = 'VERIFIED';
      } else {
        // Failed - retry based on failure count
        const failureCount = domain.verificationFailureCount + 1;
        if (failureCount >= 10) {
          // Too many failures - check daily
          nextCheck = new Date(now.getTime() + 24 * 60 * 60 * 1000);
          newStatus = 'FAILED';
        } else {
          // Retry in 15 minutes
          nextCheck = new Date(now.getTime() + 15 * 60 * 1000);
          newStatus = 'PENDING';
        }
      }

      // Update domain status
      await prisma.domain.update({
        where: { id: domain.id },
        data: {
          verified: verificationResult.verified,
          verificationStatus: newStatus,
          lastVerificationAttempt: now,
          nextVerificationCheck: nextCheck,
          verificationFailureCount: verificationResult.verified ? 0 : domain.verificationFailureCount + 1,
          updatedAt: now,
        },
      });

      // Log verification attempt
      await prisma.auditLog.create({
        data: {
          action: 'domain_verification_auto',
          resourceId: domain.id,
          resourceType: 'domain',
          metadata: {
            domain: domain.domain,
            verified: verificationResult.verified,
            automatic: true,
            failureCount: verificationResult.verified ? 0 : domain.verificationFailureCount + 1,
            nextCheck: nextCheck.toISOString(),
            error: verificationResult.error,
          },
          expiresAt: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000), // 30 days
        },
      });

      logger.info({ 
        domain: domain.domain, 
        verified: verificationResult.verified,
        nextCheck: nextCheck.toISOString()
      }, 'Domain verification completed');

    } catch (error) {
      logger.error({ domain: domain.domain, error }, 'Failed to verify domain');
    }
  }
}