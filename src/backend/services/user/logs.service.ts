import { prisma } from '../../lib/prisma.js';
import { logger } from '../../utils/logger.js';

interface LogsFilter {
  domainId?: string;
  aliasId?: string;
  status?: 'PENDING' | 'DELIVERED' | 'FAILED' | 'RETRYING' | 'EXPIRED';
  limit: number;
  offset: number;
}

export class LogsService {
  /**
   * Get email logs for a user with filtering and pagination
   */
  async getUserLogs(userId: string, filter: LogsFilter) {
    try {
      // Build the where clause for filtering
      const whereClause: any = {
        domain: { userId }
      };

      // Filter by domain if specified
      if (filter.domainId) {
        whereClause.domainId = filter.domainId;
      }

      // Filter by delivery status if specified
      if (filter.status) {
        whereClause.deliveryStatus = filter.status;
      }

      // Filter by alias if specified
      // This is more complex because we need to match the email address to an alias
      if (filter.aliasId) {
        // First, get the alias to find its email address
        const alias = await prisma.alias.findFirst({
          where: {
            id: filter.aliasId,
            domain: { userId }
          }
        });

        if (alias) {
          // Filter emails that were sent to this specific alias email
          whereClause.toAddresses = {
            has: alias.email
          };
        } else {
          // If alias not found, return empty result
          return {
            logs: [],
            total: 0,
            hasMore: false
          };
        }
      }

      // Get total count for pagination
      const total = await prisma.email.count({
        where: whereClause
      });

      // Get the logs with pagination
      const logs = await prisma.email.findMany({
        where: whereClause,
        include: {
          domain: {
            select: {
              id: true,
              domain: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: filter.limit,
        skip: filter.offset
      });

      // Format the response
      const formattedLogs = logs.map(log => ({
        id: log.id,
        messageId: log.messageId,
        fromAddress: log.fromAddress,
        toAddresses: log.toAddresses,
        subject: log.subject,
        deliveryStatus: log.deliveryStatus,
        deliveryAttempts: log.deliveryAttempts,
        lastAttemptAt: log.lastAttemptAt?.toISOString() || null,
        deliveredAt: log.deliveredAt?.toISOString() || null,
        errorMessage: log.errorMessage,
        createdAt: log.createdAt.toISOString(),
        expiresAt: log.expiresAt.toISOString(),
        domain: {
          id: log.domain.id,
          domain: log.domain.domain
        }
      }));

      const hasMore = filter.offset + filter.limit < total;

      logger.debug({
        userId,
        filter,
        totalLogs: total,
        returnedLogs: formattedLogs.length,
        hasMore
      }, 'Retrieved user logs');

      return {
        logs: formattedLogs,
        total,
        hasMore
      };

    } catch (error: any) {
      logger.error({
        userId,
        filter,
        error: error.message,
        stack: error.stack
      }, 'Failed to retrieve user logs');
      
      throw new Error('Failed to retrieve logs');
    }
  }
}
