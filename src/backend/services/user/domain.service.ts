import { prisma } from '../../lib/prisma.js';
import { PostfixManager } from '../postfix-manager.js';
import { DNSVerifier, DNSVerificationResult } from '../dns-verifier.js';
import { logger } from '../../utils/logger.js';

const postfixManager = new PostfixManager();
const dnsVerifier = new DNSVerifier();

export interface CreateDomainData {
  domain: string;
  webhookUrl?: string;
  webhookId?: string;
  active?: boolean;
  createCatchAll?: boolean;
  userId: string;
}

export interface UpdateDomainData {
  active?: boolean;
  webhookId?: string;
}

export class DomainService {
  /**
   * Get all domains for a user
   */
  async getUserDomains(userId: string) {
    const dbDomains = await prisma.domain.findMany({
      where: { userId },
      include: {
        aliases: {
          include: { webhook: true },
          where: { active: true }
        },
        webhook: true
      },
      orderBy: { createdAt: 'desc' },
    });


    
    // Make postfix call resilient to failures
    let postfixDomainsList: string[] = [];
    try {
      postfixDomainsList = await postfixManager.getConfiguredDomains();
    } catch (error: any) {
      logger.warn({ error: error.message }, 'Failed to get postfix domains, continuing without');
    }

    const domains = dbDomains.map(d => {
      const mappedDomain = {
        id: d.id,
        domainName: d.domain,
        webhook: d.webhook ? {
          id: d.webhook.id,
          name: d.webhook.name,
          url: d.webhook.url,
          verified: d.webhook.verified
        } : null,
        active: d.active,
        isVerified: d.verified,
        verificationStatus: d.verificationStatus,
        postfix_configured: postfixDomainsList.includes(d.domain),
        createdAt: d.createdAt.toISOString(),
        updatedAt: d.updatedAt.toISOString(),
        aliases: d.aliases.map(alias => ({
          id: alias.id,
          localPart: alias.email.includes('@') ? alias.email.split('@')[0] : alias.email,
          destinationEmail: alias.webhook?.url,
          webhookName: alias.webhook?.name,
          active: alias.active,
        })),
        lastVerificationAttempt: d.lastVerificationAttempt?.toISOString(),
        verificationFailureCount: d.verificationFailureCount,
        nextVerificationCheck: d.nextVerificationCheck?.toISOString(),
        expectedTxtRecord: d.domain ? DNSVerifier.getExpectedTXTRecord(d.domain) : null,
      };

      return mappedDomain;
    });

    return {
      domains,
      total: domains.length,
      postfix_status: 'active',
      verified_count: domains.filter(d => d.isVerified).length,
      pending_verification: domains.filter(d => d.verificationStatus === 'PENDING').length,
    };
  }

  /**
   * Get a specific domain by name for a user
   */
  async getDomainByName(domainName: string, userId: string) {
    const domainConfig = await prisma.domain.findFirst({ 
      where: { 
        domain: domainName,
        userId  
      }, 
      include: { 
        aliases: { include: { webhook: true } },
        webhook: true 
      } 
    });

    if (!domainConfig) {
      return null;
    }
    
    // Make postfix call resilient to failures
    let postfixList: string[] = [];
    try {
      postfixList = await postfixManager.getConfiguredDomains();
    } catch (error: any) {
      logger.warn({ error: error.message }, 'Failed to get postfix domains, continuing without');
    }

    return {
      id: domainConfig.id, 
      domain: domainConfig.domain, 
      webhookUrl: domainConfig.webhook?.url, 
      webhookName: domainConfig.webhook?.name,
      active: domainConfig.active,
      isVerified: domainConfig.verified, 
      verificationStatus: domainConfig.verificationStatus,
      postfix_configured: postfixList.includes(domainConfig.domain),
      createdAt: domainConfig.createdAt.toISOString(), 
      updatedAt: domainConfig.updatedAt.toISOString(),
      aliases: domainConfig.aliases.map((a: any) => ({ 
        id: a.id, 
        localPart: a.email.split('@')[0], 
        destinationEmail: a.webhook?.url, 
        webhookName: a.webhook?.name,
        active: a.active 
      })),
      lastVerificationAttempt: domainConfig.lastVerificationAttempt?.toISOString(),
      verificationFailureCount: domainConfig.verificationFailureCount,
      nextVerificationCheck: domainConfig.nextVerificationCheck?.toISOString(),
      expectedTxtRecord: DNSVerifier.getExpectedTXTRecord(domainConfig.domain),
    };
  }

  /**
   * Create a new domain
   */
  async createDomain(data: CreateDomainData) {
    // Validate domain format
    if (!DNSVerifier.isValidDomain(data.domain)) {
      throw new Error('Invalid domain format');
    }

    if (!data.webhookUrl && !data.webhookId) {
      throw new Error('Either webhookUrl or webhookId is required');
    }

    const result = await prisma.$transaction(async (tx) => {
      // Get user
      const currentUser = await tx.user.findUnique({
        where: { id: data.userId }
      });
      
      if (!currentUser) {
        throw new Error('User not found');
      }
      
      // Get webhook for domain default
      let domainWebhook;

      if (data.webhookId) {
        // Use existing webhook by ID
        domainWebhook = await tx.webhook.findFirst({
          where: { id: data.webhookId, userId: currentUser.id }
        });

        if (!domainWebhook) {
          throw new Error('Webhook not found or does not belong to user');
        }
      } else if (data.webhookUrl) {
        // Legacy support: create or find webhook by URL
        domainWebhook = await tx.webhook.findFirst({
          where: { url: data.webhookUrl, userId: currentUser.id }
        });

        if (!domainWebhook) {
          domainWebhook = await tx.webhook.create({
            data: {
              url: data.webhookUrl,
              name: `Default webhook for ${data.domain}`,
              description: `Auto-created webhook for ${data.domain}`,
              userId: currentUser.id
            }
          });
        }
      }

      const newDomain = await tx.domain.create({
        data: {
          domain: data.domain,
          userId: currentUser.id,
          webhookId: domainWebhook!.id,
          active: data.active ?? true,
          verified: false,
          verificationStatus: 'PENDING',
          nextVerificationCheck: new Date(Date.now() + 15 * 60 * 1000),
        },
      });

      // Create catch-all alias if requested
      if (data.createCatchAll) {
        await tx.alias.create({
          data: {
            domainId: newDomain.id,
            email: `*@${newDomain.domain}`,
            webhookId: domainWebhook!.id,
            active: true,
          },
        });
      }

      await postfixManager.addDomain(data.domain);

      // Debug logging
      console.log('Transaction result - domainWebhook:', domainWebhook);
      console.log('Transaction result - newDomain:', newDomain);

      if (!domainWebhook) {
        throw new Error('domainWebhook is undefined after webhook resolution');
      }

      return { domain: newDomain, webhook: domainWebhook };
    });

    const instructions = {
      mx_record: {
        type: 'MX',
        name: '@',
        value: process.env.MAIL_HOSTNAME || 'your-mail-server.example.com',
        priority: 10
      },
      txt_record: {
        type: 'TXT',
        name: '@',
        value: DNSVerifier.getExpectedTXTRecord(result.domain.domain)
      },
      next_steps: [
        'Add the MX record to your DNS settings',
        'Add the TXT record for domain verification',
        'Wait for DNS propagation (up to 24 hours)',
        'Use the verify endpoint to check verification status'
      ]
    };

    const responseDomain = {
      id: result.domain.id,
      domain: result.domain.domain,
      apiKey: null,
      dkimPublicKey: null,
      dkimSelector: null,
      isVerified: result.domain.verified,
      verificationToken: null,
      createdAt: result.domain.createdAt.toISOString(),
      updatedAt: result.domain.updatedAt.toISOString(),
    };

    // Include webhook information for verification flow
    if (!result.webhook) {
      throw new Error('Webhook information missing from domain creation result');
    }

    const webhookInfo = {
      id: result.webhook.id,
      url: result.webhook.url,
      name: result.webhook.name,
      verified: result.webhook.verified
    };

    return {
      success: true,
      domain: responseDomain,
      webhook: webhookInfo,
      instructions,
      postfix_status: 'configured'
    };
  }

  /**
   * Update a domain
   */
  async updateDomain(domainName: string, userId: string, updates: UpdateDomainData) {
    const existing = await prisma.domain.findFirst({
      where: { domain: domainName, userId }
    });

    if (!existing) {
      throw new Error('Domain not found');
    }

    const dataToUpdate: any = { updatedAt: new Date() };
    if (updates.active !== undefined) dataToUpdate.active = updates.active;
    if (updates.webhookId !== undefined) {
      // Verify webhook belongs to user
      const webhook = await prisma.webhook.findFirst({
        where: { id: updates.webhookId, userId }
      });
      if (!webhook) {
        throw new Error('Webhook not found');
      }
      dataToUpdate.webhookId = updates.webhookId;
    }

    const updated = await prisma.domain.update({ 
      where: { domain: domainName }, 
      data: dataToUpdate 
    });

    const responseDomain = { 
      id: updated.id, 
      domain: updated.domain, 
      apiKey: null, 
      dkimPublicKey: null, 
      dkimSelector: null, 
      isVerified: updated.verified, 
      verificationToken: null, 
      createdAt: updated.createdAt.toISOString(), 
      updatedAt: updated.updatedAt.toISOString() 
    };

    return {
      success: true,
      domain: responseDomain,
      message: 'Domain updated successfully'
    };
  }

  /**
   * Update domain status by ID
   */
  async updateDomainStatus(domainId: string, userId: string, active: boolean) {
    // Verify domain belongs to user
    const existing = await prisma.domain.findFirst({
      where: {
        id: domainId,
        userId
      }
    });

    if (!existing) {
      throw new Error('Domain not found');
    }

    // Update the domain status
    const updated = await prisma.domain.update({
      where: { id: domainId },
      data: {
        active: active,
        updatedAt: new Date()
      }
    });

    return {
      success: true,
      domain: {
        id: updated.id,
        domain: updated.domain,
        active: updated.active,
        updatedAt: updated.updatedAt.toISOString()
      },
      message: `Domain ${updated.domain} ${active ? 'activated' : 'deactivated'} successfully`
    };
  }

  /**
   * Update domain webhook by ID
   */
  async updateDomainWebhook(domainId: string, userId: string, webhookId: string) {
    // Verify domain belongs to user
    const existingDomain = await prisma.domain.findFirst({
      where: {
        id: domainId,
        userId
      }
    });

    if (!existingDomain) {
      throw new Error('Domain not found');
    }

    // Verify webhook belongs to user
    const webhook = await prisma.webhook.findFirst({
      where: {
        id: webhookId,
        userId
      }
    });

    if (!webhook) {
      throw new Error('Webhook not found');
    }

    // Update the domain webhook
    const updated = await prisma.domain.update({
      where: { id: domainId },
      data: {
        webhookId: webhookId,
        updatedAt: new Date()
      }
    });

    return {
      success: true,
      domain: {
        id: updated.id,
        domain: updated.domain,
        webhookId: updated.webhookId,
        updatedAt: updated.updatedAt.toISOString()
      },
      webhook: {
        id: webhook.id,
        name: webhook.name,
        url: webhook.url
      },
      message: `Domain webhook updated to "${webhook.name}"`
    };
  }

  /**
   * Delete a domain
   */
  async deleteDomain(domainName: string, userId: string) {
    await prisma.$transaction(async tx => {
      const d = await tx.domain.findFirst({
        where: { domain: domainName, userId },
        include: { emails: true, aliases: true }
      });

      if (!d) {
        throw new Error('Domain not found');
      }

      await tx.domain.delete({ where: { domain: domainName } });
      await postfixManager.removeDomain(domainName);

      await tx.auditLog.create({
        data: {
          action: 'domain_deleted',
          resourceId: d.id,
          resourceType: 'domain',
          metadata: {
            domain: domainName,
            emailsDeleted: d.emails.length,
            aliasesDeleted: d.aliases.length
          },
          expiresAt: new Date(Date.now() + 90*24*60*60*1000)
        }
      });
    });

    return {
      success: true,
      domain: domainName,
      message: 'Domain deleted successfully',
      postfix_status: 'removed'
    };
  }

  /**
   * Verify a domain
   */
  async verifyDomain(domainName: string, userId: string) {
    const dCfg = await prisma.domain.findFirst({
      where: { domain: domainName, userId }
    });

    if (!dCfg) {
      throw new Error('Domain not found');
    }

    // Rate limiting check
    if (dCfg.lastVerificationAttempt &&
        (Date.now() - dCfg.lastVerificationAttempt.getTime()) < 60000) {
      throw new Error('Rate limit exceeded. Please wait before trying again.');
    }

    const vRes: DNSVerificationResult = await dnsVerifier.verifyDomainOwnership(domainName);
    const fCnt = dCfg.verificationFailureCount || 0;
    const nFCnt = vRes.verified ? 0 : fCnt + 1;
    const MAX_FAILS = 5;

    const updated = await prisma.domain.update({
      where: { domain: domainName },
      data: {
        lastVerificationAttempt: new Date(),
        verificationFailureCount: nFCnt,
        verified: vRes.verified ? true : dCfg.verified,
        verificationStatus: vRes.verified ? 'VERIFIED' : (nFCnt >= MAX_FAILS ? 'FAILED' : 'PENDING'),
        nextVerificationCheck: new Date(Date.now() + (vRes.verified ? 24*60*60*1000 : 15*60*1000))
      }
    });

    await prisma.auditLog.create({
      data: {
        action: 'domain_verification_attempted',
        resourceId: updated.id,
        resourceType: 'domain',
        metadata: {
          domain: domainName,
          verified: vRes.verified,
          manual: true,
          foundRecords: vRes.foundRecords,
          error: vRes.error
        },
        expiresAt: new Date(Date.now() + 90*24*60*60*1000)
      }
    });

    const dnsRecs = vRes.expectedRecord ? [{
      type: 'TXT',
      name: '@',
      value: vRes.expectedRecord,
      status: updated.verified ? 'verified' : 'pending'
    }] : [];

    let timestampStr = new Date().toISOString();
    if (vRes.timestamp) {
      if (typeof vRes.timestamp === 'string') {
        const d = new Date(vRes.timestamp);
        if (!isNaN(d.getTime())) {
          timestampStr = d.toISOString();
        } else {
          timestampStr = vRes.timestamp;
        }
      } else if (typeof vRes.timestamp === 'number') {
        timestampStr = new Date(vRes.timestamp).toISOString();
      }
    }

    return {
      success: true,
      domain: domainName,
      verification: {
        verified: vRes.verified,
        status: updated.verificationStatus,
        timestamp: timestampStr,
        expectedRecord: vRes.expectedRecord,
        foundRecords: vRes.foundRecords,
        cached: vRes.cached,
        error: vRes.error
      },
      domain_status: {
        domain: updated.domain,
        isVerified: updated.verified,
        dnsRecords: dnsRecs
      },
      message: updated.verified ?
        'Domain successfully verified!' :
        `Verification ${vRes.verified ? 'OK':'Failed'}. Status: ${updated.verificationStatus}. ${vRes.error||''}`
    };
  }
}
