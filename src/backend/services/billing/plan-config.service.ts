import { env } from '../../config/env.js';

export interface PlanConfig {
  name: string;
  monthlyEmailLimit: number;
  features: string[];
  price?: {
    monthly: number;
    yearly: number;
    currency: string;
  };
}

export interface PlanLimits {
  monthlyEmails: number;
  domains?: number;
  webhooks?: number;
  aliases?: number;
}

export class PlanConfigService {
  private static readonly PLAN_CONFIGS: Record<string, PlanConfig> = {
    free: {
      name: 'Free',
      monthlyEmailLimit: env.FREE_PLAN_EMAIL_LIMIT,
      features: [
        'Up to 50 emails per month',
        'Basic webhook delivery',
        'Email forwarding',
        'Community support'
      ]
    },
    pro: {
      name: 'Pro',
      monthlyEmailLimit: env.PRO_PLAN_EMAIL_LIMIT,
      features: [
        'Up to 1,000 emails per month',
        'Priority webhook delivery',
        'Advanced email routing',
        'Email analytics',
        'Priority support'
      ],
      price: {
        monthly: 9.99,
        yearly: 99.99,
        currency: 'USD'
      }
    },
    enterprise: {
      name: 'Enterprise',
      monthlyEmailLimit: env.ENTERPRISE_PLAN_EMAIL_LIMIT,
      features: [
        'Up to 10,000 emails per month',
        'Guaranteed webhook delivery',
        'Custom email routing',
        'Advanced analytics',
        'Dedicated support',
        'SLA guarantee'
      ],
      price: {
        monthly: 49.99,
        yearly: 499.99,
        currency: 'USD'
      }
    }
  };

  /**
   * Get configuration for a specific plan
   */
  static getPlanConfig(planType: string): PlanConfig {
    const config = this.PLAN_CONFIGS[planType];
    if (!config) {
      throw new Error(`Unknown plan type: ${planType}`);
    }
    return config;
  }

  /**
   * Get all available plans
   */
  static getAllPlans(): Record<string, PlanConfig> {
    return { ...this.PLAN_CONFIGS };
  }

  /**
   * Get plan limits for a specific plan type
   */
  static getPlanLimits(planType: string): PlanLimits {
    const config = this.getPlanConfig(planType);
    return {
      monthlyEmails: config.monthlyEmailLimit,
      // Future: Add other limits like domains, webhooks, etc.
      domains: planType === 'free' ? 3 : planType === 'pro' ? 10 : 50,
      webhooks: planType === 'free' ? 2 : planType === 'pro' ? 5 : 20,
      aliases: planType === 'free' ? 10 : planType === 'pro' ? 50 : 200
    };
  }

  /**
   * Check if a plan upgrade is valid
   */
  static isValidUpgrade(currentPlan: string, targetPlan: string): boolean {
    const planHierarchy = ['free', 'pro', 'enterprise'];
    const currentIndex = planHierarchy.indexOf(currentPlan);
    const targetIndex = planHierarchy.indexOf(targetPlan);
    
    return currentIndex !== -1 && targetIndex !== -1 && targetIndex > currentIndex;
  }

  /**
   * Check if a plan downgrade is valid
   */
  static isValidDowngrade(currentPlan: string, targetPlan: string): boolean {
    const planHierarchy = ['free', 'pro', 'enterprise'];
    const currentIndex = planHierarchy.indexOf(currentPlan);
    const targetIndex = planHierarchy.indexOf(targetPlan);
    
    return currentIndex !== -1 && targetIndex !== -1 && targetIndex < currentIndex;
  }

  /**
   * Get the default monthly email limit for new users
   */
  static getDefaultEmailLimit(): number {
    return env.DEFAULT_MONTHLY_EMAIL_LIMIT;
  }

  /**
   * Validate if current usage fits within target plan limits
   */
  static validateUsageForPlan(
    planType: string, 
    currentUsage: { 
      domains: number; 
      webhooks: number; 
      aliases: number; 
    }
  ): { valid: boolean; violations: string[] } {
    const limits = this.getPlanLimits(planType);
    const violations: string[] = [];

    if (currentUsage.domains > (limits.domains || Infinity)) {
      violations.push(`Too many domains (${currentUsage.domains}/${limits.domains})`);
    }
    
    if (currentUsage.webhooks > (limits.webhooks || Infinity)) {
      violations.push(`Too many webhooks (${currentUsage.webhooks}/${limits.webhooks})`);
    }
    
    if (currentUsage.aliases > (limits.aliases || Infinity)) {
      violations.push(`Too many aliases (${currentUsage.aliases}/${limits.aliases})`);
    }

    return {
      valid: violations.length === 0,
      violations
    };
  }
}
