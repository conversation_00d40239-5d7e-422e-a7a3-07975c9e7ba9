import { prisma } from '../../lib/prisma.js';
import { mollieService } from './mollie.service.js';
import { logger } from '../../utils/logger.js';
import { PlanConfigService } from '../billing/plan-config.service.js';
import { env } from '../../config/env.js';

export interface CreatePaymentRequest {
  userId: string;
  planType: 'pro' | 'enterprise';
  interval: 'monthly' | 'yearly';
  successUrl: string;
  cancelUrl: string;
}

export interface CreateSubscriptionRequest {
  userId: string;
  planType: 'pro' | 'enterprise';
  interval: 'monthly' | 'yearly';
  mollieCustomerId: string;
}

export class PaymentWorkflowService {
  /**
   * Create a one-time payment for plan upgrade
   */
  async createPayment(request: CreatePaymentRequest) {
    try {
      // Get plan configuration
      const planConfig = PlanConfigService.getPlanConfig(request.planType);
      if (!planConfig.price) {
        throw new Error(`Plan ${request.planType} does not have pricing configured`);
      }

      // Calculate amount based on interval
      const amount = request.interval === 'yearly' 
        ? planConfig.price.yearly 
        : planConfig.price.monthly;

      // Get user for metadata
      const user = await prisma.user.findUnique({
        where: { id: request.userId },
        select: { email: true, name: true }
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Create payment with Mollie
      const molliePayment = await mollieService.createPayment({
        amount: {
          value: amount.toFixed(2),
          currency: planConfig.price.currency
        },
        description: `${planConfig.name} Plan - ${request.interval}`,
        redirectUrl: request.successUrl,
        webhookUrl: env.MOLLIE_WEBHOOK_URL,
        metadata: {
          userId: request.userId,
          planType: request.planType,
          interval: request.interval,
          userEmail: user.email
        }
        // No method specified - let Mollie show all available payment methods
      });

      // Store payment in database
      const payment = await prisma.payment.create({
        data: {
          mollieId: molliePayment.id,
          status: 'PENDING',
          amount: amount,
          currency: planConfig.price.currency,
          description: `${planConfig.name} Plan - ${request.interval}`,
          userId: request.userId,
          mollieWebhookData: molliePayment as any
        }
      });

      logger.info({
        paymentId: payment.id,
        mollieId: molliePayment.id,
        userId: request.userId,
        planType: request.planType,
        amount: amount
      }, 'Payment created successfully');

      return {
        paymentId: payment.id,
        mollieId: molliePayment.id,
        checkoutUrl: molliePayment.getCheckoutUrl(),
        amount: {
          value: amount.toFixed(2),
          currency: planConfig.price.currency
        }
      };
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId: request.userId,
        planType: request.planType
      }, 'Failed to create payment');
      throw error;
    }
  }

  /**
   * Create a recurring subscription
   */
  async createSubscription(request: CreateSubscriptionRequest) {
    try {
      // Get plan configuration
      const planConfig = PlanConfigService.getPlanConfig(request.planType);
      if (!planConfig.price) {
        throw new Error(`Plan ${request.planType} does not have pricing configured`);
      }

      // Calculate amount based on interval
      const amount = request.interval === 'yearly' 
        ? planConfig.price.yearly 
        : planConfig.price.monthly;

      // Create subscription with Mollie
      const mollieSubscription = await mollieService.createSubscription({
        customerId: request.mollieCustomerId,
        amount: {
          value: amount.toFixed(2),
          currency: planConfig.price.currency
        },
        interval: request.interval === 'yearly' ? '12 months' : '1 month',
        description: `${planConfig.name} Plan`,
        metadata: {
          userId: request.userId,
          planType: request.planType
        }
      });

      // Store subscription in database
      const subscription = await prisma.subscription.create({
        data: {
          mollieId: mollieSubscription.id,
          status: 'PENDING',
          planType: request.planType,
          interval: request.interval,
          amount: amount,
          currency: planConfig.price.currency,
          description: `${planConfig.name} Plan`,
          mollieCustomerId: request.mollieCustomerId,
          userId: request.userId,
          startDate: new Date(),
          nextPaymentDate: mollieSubscription.nextPaymentDate ? new Date(mollieSubscription.nextPaymentDate) : null
        }
      });

      logger.info({
        subscriptionId: subscription.id,
        mollieId: mollieSubscription.id,
        userId: request.userId,
        planType: request.planType
      }, 'Subscription created successfully');

      return subscription;
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId: request.userId,
        planType: request.planType
      }, 'Failed to create subscription');
      throw error;
    }
  }

  /**
   * Process payment webhook from Mollie
   */
  async processPaymentWebhook(molliePaymentId: string) {
    try {
      // Get payment details from Mollie
      const molliePayment = await mollieService.getPayment(molliePaymentId);

      // Find payment in database
      const payment = await prisma.payment.findUnique({
        where: { mollieId: molliePaymentId },
        include: { user: true }
      });

      if (!payment) {
        logger.warn({ molliePaymentId }, 'Payment not found in database');
        return;
      }

      // Update payment status
      const updateData: any = {
        status: this.mapMollieStatusToPaymentStatus(molliePayment.status),
        mollieWebhookData: molliePayment as any
      };

      // Set timestamps based on status
      if (molliePayment.status === 'paid' && molliePayment.paidAt) {
        updateData.paidAt = new Date(molliePayment.paidAt);
      } else if (molliePayment.status === 'cancelled' && molliePayment.cancelledAt) {
        updateData.cancelledAt = new Date(molliePayment.cancelledAt);
      } else if (molliePayment.status === 'expired' && molliePayment.expiredAt) {
        updateData.expiredAt = new Date(molliePayment.expiredAt);
      } else if (molliePayment.status === 'failed' && molliePayment.failedAt) {
        updateData.failedAt = new Date(molliePayment.failedAt);
        updateData.failureReason = molliePayment.details?.failureReason;
      }

      await prisma.payment.update({
        where: { id: payment.id },
        data: updateData
      });

      // If payment is successful, upgrade user's plan
      if (molliePayment.status === 'paid' && molliePayment.metadata) {
        await this.upgradeUserPlan(
          payment.userId,
          molliePayment.metadata.planType as string,
          molliePayment.metadata.interval as string
        );
      }

      logger.info({
        paymentId: payment.id,
        mollieId: molliePaymentId,
        status: molliePayment.status,
        userId: payment.userId
      }, 'Payment webhook processed');

    } catch (error: any) {
      logger.error({
        error: error.message,
        molliePaymentId
      }, 'Failed to process payment webhook');
      throw error;
    }
  }

  /**
   * Upgrade user's plan after successful payment
   */
  private async upgradeUserPlan(userId: string, planType: string, interval: string) {
    try {
      const planConfig = PlanConfigService.getPlanConfig(planType);
      
      await prisma.user.update({
        where: { id: userId },
        data: {
          planType: planType,
          monthlyEmailLimit: planConfig.monthlyEmailLimit
        }
      });

      logger.info({
        userId,
        planType,
        interval,
        emailLimit: planConfig.monthlyEmailLimit
      }, 'User plan upgraded successfully');

    } catch (error: any) {
      logger.error({
        error: error.message,
        userId,
        planType
      }, 'Failed to upgrade user plan');
      throw error;
    }
  }

  /**
   * Map Mollie payment status to our payment status enum
   */
  private mapMollieStatusToPaymentStatus(mollieStatus: string): string {
    const statusMap: Record<string, string> = {
      'open': 'PENDING',
      'pending': 'PENDING',
      'paid': 'PAID',
      'cancelled': 'CANCELLED',
      'expired': 'EXPIRED',
      'failed': 'FAILED',
      'authorized': 'AUTHORIZED'
    };

    return statusMap[mollieStatus] || 'PENDING';
  }

  /**
   * Get user's payment history
   */
  async getUserPayments(userId: string, limit: number = 10) {
    try {
      const payments = await prisma.payment.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: limit,
        select: {
          id: true,
          mollieId: true,
          status: true,
          amount: true,
          currency: true,
          description: true,
          method: true,
          paidAt: true,
          createdAt: true
        }
      });

      return payments.map(payment => ({
        id: payment.id,
        amount: {
          value: payment.amount.toString(),
          currency: payment.currency
        },
        description: payment.description || '',
        status: payment.status.toLowerCase(),
        method: payment.method,
        paidAt: payment.paidAt?.toISOString(),
        createdAt: payment.createdAt.toISOString()
      }));
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId
      }, 'Failed to get user payments');
      throw error;
    }
  }
}

export const paymentWorkflowService = new PaymentWorkflowService();
