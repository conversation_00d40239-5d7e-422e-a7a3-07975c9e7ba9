import { FastifyRequest, FastifyReply } from 'fastify';
import { UserAuthService, UserRegisterData, UserLoginData } from '../../services/auth/user-auth.service.js';
import { logger } from '../../utils/logger.js';

const userAuthService = new UserAuthService();

export class UserAuthController {
  /**
   * Handle user registration
   */
  async register(request: FastifyRequest, reply: FastifyReply) {
    try {
      // Handle both JSON and form data
      let userData: UserRegisterData;

      if (request.headers['content-type']?.includes('application/json')) {
        userData = request.body as UserRegisterData;
      } else {
        // Form data
        userData = request.body as any;
      }

      const result = await userAuthService.registerUser(userData);

      if (!result.success) {
        const statusCode = result.error?.includes('required') || 
                          result.error?.includes('Invalid email') ||
                          result.error?.includes('Password must') ? 400 :
                          result.error?.includes('already exists') ? 409 : 500;
        return reply.status(statusCode).send({ error: result.error });
      }

      // Set cookie
      const cookieConfig = userAuthService.getCookieConfig();
      reply.setCookie('user_token', result.token!, cookieConfig);

      // Redirect to dashboard instead of returning JSON for form submissions
      if (!request.headers['content-type']?.includes('application/json')) {
        return reply.status(302).redirect('/dashboard');
      }

      return reply.status(201).send({
        message: 'Registration successful',
        user: result.user,
        token: result.token
      });

    } catch (error: any) {
      logger.error({ err: error }, 'Error in user registration controller');
      return reply.status(500).send({ 
        error: 'Internal server error during registration' 
      });
    }
  }

  /**
   * Handle user login
   */
  async login(request: FastifyRequest, reply: FastifyReply) {
    try {
      const credentials = request.body as UserLoginData;

      const result = await userAuthService.loginUser(credentials);

      if (!result.success) {
        const statusCode = result.error?.includes('required') ? 400 : 
                          result.error?.includes('Invalid credentials') ? 401 : 500;
        return reply.status(statusCode).send({ error: result.error });
      }

      // Set cookie
      const cookieConfig = userAuthService.getCookieConfig();
      reply.setCookie('user_token', result.token!, cookieConfig);

      return reply.send({
        message: 'Login successful',
        user: result.user,
        token: result.token
      });

    } catch (error: any) {
      logger.error({ err: error }, 'Error in user login controller');
      return reply.status(500).send({ 
        error: 'Internal server error during authentication' 
      });
    }
  }

  /**
   * Handle user logout
   */
  async logout(request: FastifyRequest, reply: FastifyReply) {
    try {
      // Clear the user token cookie
      reply.clearCookie('user_token', { path: '/' });
      
      return reply.send({ message: 'Logout successful' });
    } catch (error: any) {
      logger.error({ err: error }, 'Error in user logout controller');
      return reply.status(500).send({ 
        error: 'Internal server error during logout' 
      });
    }
  }

  /**
   * Get current user profile
   */
  async getProfile(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      
      // In a real app, you might want to fetch fresh user data from the database
      // For now, we'll return the user data from the JWT token
      return reply.send({
        user: {
          id: user.id,
          email: user.email
        }
      });
    } catch (error: any) {
      logger.error({ err: error }, 'Error in get user profile controller');
      return reply.status(500).send({ 
        error: 'Internal server error retrieving profile' 
      });
    }
  }
}
