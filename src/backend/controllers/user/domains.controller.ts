import { FastifyRequest, FastifyReply } from 'fastify';
import { DomainService, CreateDomainData, UpdateDomainData } from '../../services/user/domain.service.js';
import { logger } from '../../utils/logger.js';

const domainService = new DomainService();

export class DomainsController {
  /**
   * Get user's domains
   */
  async getDomains(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const result = await domainService.getUserDomains(user.id);
      
      return reply.send(result);
    } catch (error: any) {
      logger.error({ 
        error: error?.message, 
        stack: error.stack, 
        userId: (request as any).user?.id 
      }, 'Failed to get domain configurations');
      
      return reply.code(500).send({ 
        statusCode: 500, 
        error: 'Internal Server Error', 
        message: 'Failed to retrieve domains', 
        details: error.message 
      });
    }
  }

  /**
   * Get specific domain by name
   */
  async getDomain(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const { domain: domainName } = request.params as { domain: string };

      const domain = await domainService.getDomainByName(domainName, user.id);

      if (!domain) {
        return reply.code(404).send({ 
          statusCode: 404, 
          error: 'Not Found', 
          message: 'Domain not found' 
        });
      }

      return reply.send(domain);
    } catch (error: any) {
      logger.error(error);
      return reply.code(500).send({
        statusCode: 500, 
        error: 'Internal Server Error', 
        message: error.message
      });
    }
  }

  /**
   * Create new domain
   */
  async createDomain(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const body = request.body as Omit<CreateDomainData, 'userId'>;

      // Validate required fields
      if (!body.domain) {
        return reply.code(400).send({ 
          statusCode: 400, 
          error: 'Bad Request', 
          message: 'Domain is required' 
        });
      }

      if (!body.webhookUrl && !body.webhookId) {
        return reply.code(400).send({ 
          statusCode: 400, 
          error: 'Bad Request', 
          message: 'Either webhookUrl or webhookId is required' 
        });
      }

      logger.info({ domain: body.domain }, 'Adding new domain');

      const createData: CreateDomainData = {
        ...body,
        userId: user.id
      };

      const result = await domainService.createDomain(createData);

      logger.info({ domain: result.domain.domain, domainId: result.domain.id }, 'Domain added');

      return reply.code(201).send(result);
    } catch (error: any) {
      logger.error({ error: error.message, stack: error.stack }, 'Failed to add domain');
      
      if (error.message === 'Invalid domain format') {
        return reply.code(400).send({ 
          statusCode: 400, 
          error: 'Bad Request', 
          message: error.message 
        });
      }

      if (error.message === 'User not found' || 
          error.message === 'Webhook not found or does not belong to user') {
        return reply.code(400).send({ 
          statusCode: 400, 
          error: 'Bad Request', 
          message: error.message 
        });
      }

      if (error.code === 'P2002') {
        return reply.code(409).send({ 
          statusCode: 409, 
          error: 'Conflict', 
          message: 'Domain already exists' 
        });
      }

      return reply.code(500).send({ 
        statusCode: 500, 
        error: 'Internal Server Error', 
        message: 'Failed to configure domain' 
      });
    }
  }

  /**
   * Update domain
   */
  async updateDomain(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const { domain: domainName } = request.params as { domain: string };
      const updates = request.body as UpdateDomainData;

      const result = await domainService.updateDomain(domainName, user.id, updates);

      return reply.send(result);
    } catch (error: any) {
      if (error.message === 'Domain not found') {
        return reply.code(404).send({
          statusCode: 404, 
          error: 'Not Found', 
          message: error.message
        });
      }

      if (error.message === 'Webhook not found') {
        return reply.code(400).send({
          statusCode: 400, 
          error: 'Bad Request', 
          message: error.message
        });
      }

      logger.error(error);
      return reply.code(500).send({
        statusCode: 500, 
        error: 'Internal Server Error', 
        message: error.message
      });
    }
  }

  /**
   * Update domain status
   */
  async updateDomainStatus(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const { id: domainId } = request.params as { id: string };
      const { active } = request.body as { active: boolean };

      const result = await domainService.updateDomainStatus(domainId, user.id, active);

      return reply.send(result);
    } catch (error: any) {
      if (error.message === 'Domain not found') {
        return reply.code(404).send({
          statusCode: 404, 
          error: 'Not Found', 
          message: error.message
        });
      }

      logger.error({ 
        error: error?.message, 
        stack: error.stack, 
        domainId: (request.params as any).id, 
        userId: (request as any).user.id 
      }, 'Failed to update domain status');
      
      return reply.code(500).send({
        statusCode: 500, 
        error: 'Internal Server Error', 
        message: 'Failed to update domain status'
      });
    }
  }

  /**
   * Update domain webhook
   */
  async updateDomainWebhook(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const { id: domainId } = request.params as { id: string };
      const { webhookId } = request.body as { webhookId: string };

      const result = await domainService.updateDomainWebhook(domainId, user.id, webhookId);

      return reply.send(result);
    } catch (error: any) {
      if (error.message === 'Domain not found' || error.message === 'Webhook not found') {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: error.message
        });
      }

      logger.error({ 
        error: error?.message, 
        stack: error.stack, 
        domainId: (request.params as any).id, 
        webhookId: (request.body as any).webhookId, 
        userId: (request as any).user.id 
      }, 'Failed to update domain webhook');
      
      return reply.code(500).send({
        statusCode: 500, 
        error: 'Internal Server Error', 
        message: 'Failed to update domain webhook'
      });
    }
  }

  /**
   * Delete domain
   */
  async deleteDomain(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const { domain: domainName } = request.params as { domain: string };

      const result = await domainService.deleteDomain(domainName, user.id);

      return reply.send(result);
    } catch (error: any) {
      if (error.message === 'Domain not found') {
        return reply.code(404).send({
          statusCode: 404, 
          error: 'Not Found', 
          message: 'Domain not found'
        });
      }

      logger.error(error);
      return reply.code(500).send({
        statusCode: 500, 
        error: 'Internal Server Error', 
        message: error.message
      });
    }
  }

  /**
   * Verify domain
   */
  async verifyDomain(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const params = request.params as { domain: string };
      const domainName = params.domain;

      const result = await domainService.verifyDomain(domainName, user.id);

      return reply.send(result);
    } catch (error: any) {
      const params = request.params as { domain: string };
      const domainName = params.domain;

      if (error.message === 'Domain not found') {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'Domain not found'
        });
      }

      if (error.message.includes('Rate limit exceeded')) {
        return reply.code(429).send({
          statusCode: 429,
          error: 'Too Many Requests',
          message: 'Rate limit exceeded.'
        });
      }

      logger.error({
        domain: domainName,
        error: error.message,
        stack: error.stack
      });

      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: error.message
      });
    }
  }
}
