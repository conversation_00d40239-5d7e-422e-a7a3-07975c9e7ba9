import { FastifyPluginAsync } from 'fastify';
import { userOrA<PERSON><PERSON>eyAuthMiddleware } from '../lib/auth.js';
import { LogsController } from '../controllers/user/logs.controller.js';

const logsController = new LogsController();

export const logsRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Get user's email logs
  fastify.get('/logs', {
    preHandler: [userOrApiKeyAuthMiddleware],
    schema: {
      tags: ['User Logs'],
      summary: 'List user email logs',
      description: 'Retrieves email processing logs for the authenticated user.',
      querystring: {
        type: 'object',
        properties: {
          domainId: { 
            type: 'string', 
            description: 'Filter logs by domain ID' 
          },
          aliasId: { 
            type: 'string', 
            description: 'Filter logs by alias ID' 
          },
          status: { 
            type: 'string', 
            enum: ['PENDING', 'DELIVERED', 'FAILED', 'RETRYING', 'EXPIRED'],
            description: 'Filter logs by delivery status' 
          },
          limit: { 
            type: 'integer', 
            default: 50, 
            minimum: 1, 
            maximum: 100,
            description: 'Number of logs to return' 
          },
          offset: { 
            type: 'integer', 
            default: 0, 
            minimum: 0,
            description: 'Number of logs to skip for pagination' 
          }
        }
      },
      response: {
        200: {
          description: 'A list of email logs.',
          type: 'object',
          properties: {
            logs: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  messageId: { type: 'string' },
                  fromAddress: { type: 'string' },
                  toAddresses: { 
                    type: 'array', 
                    items: { type: 'string' } 
                  },
                  subject: { type: 'string', nullable: true },
                  deliveryStatus: { 
                    type: 'string',
                    enum: ['PENDING', 'DELIVERED', 'FAILED', 'RETRYING', 'EXPIRED']
                  },
                  deliveryAttempts: { type: 'integer' },
                  lastAttemptAt: { type: 'string', format: 'date-time', nullable: true },
                  deliveredAt: { type: 'string', format: 'date-time', nullable: true },
                  errorMessage: { type: 'string', nullable: true },
                  createdAt: { type: 'string', format: 'date-time' },
                  expiresAt: { type: 'string', format: 'date-time' },
                  domain: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      domain: { type: 'string' }
                    }
                  }
                }
              }
            },
            total: { type: 'integer' },
            hasMore: { type: 'boolean' }
          }
        },
        401: { $ref: 'ErrorResponse#' },
        500: errorResponseSchema
      },
    }
  }, logsController.getLogs.bind(logsController));
};
