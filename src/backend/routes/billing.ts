import { FastifyPluginAsync } from 'fastify';
import { BillingController } from '../controllers/user/billing.controller.js';
import { userAuthMiddleware } from '../lib/auth.js';

const billingController = new BillingController();

export const billingRoutes: FastifyPluginAsync = async (fastify) => {
  // Apply user authentication to all billing routes
  fastify.addHook('preHandler', userAuthMiddleware);

  // Get current user's plan information
  fastify.get('/plan', {
    schema: {
      tags: ['Billing'],
      summary: 'Get current user plan information',
      description: 'Retrieve the current user\'s plan details, usage, and limits',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            plan: {
              type: 'object',
              properties: {
                name: { type: 'string' },
                monthlyEmailLimit: { type: 'integer' },
                features: { type: 'array', items: { type: 'string' } },
                price: {
                  type: 'object',
                  properties: {
                    monthly: { type: 'number' },
                    yearly: { type: 'number' },
                    currency: { type: 'string' }
                  }
                }
              }
            },
            usage: {
              type: 'object',
              properties: {
                emails: { type: 'integer' },
                domains: { type: 'integer' },
                webhooks: { type: 'integer' },
                aliases: { type: 'integer' }
              }
            },
            limits: {
              type: 'object',
              properties: {
                emails: { type: 'integer' },
                domains: { type: 'integer' },
                webhooks: { type: 'integer' },
                aliases: { type: 'integer' }
              }
            }
          }
        },
        404: { $ref: 'ErrorResponse#' },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, billingController.getPlanInfo);

  // Get all available plans
  fastify.get('/plans', {
    schema: {
      tags: ['Billing'],
      summary: 'Get all available plans',
      description: 'Retrieve information about all available subscription plans',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            plans: {
              type: 'object',
              additionalProperties: {
                type: 'object',
                properties: {
                  name: { type: 'string' },
                  monthlyEmailLimit: { type: 'integer' },
                  features: { type: 'array', items: { type: 'string' } },
                  price: {
                    type: 'object',
                    properties: {
                      monthly: { type: 'number' },
                      yearly: { type: 'number' },
                      currency: { type: 'string' }
                    }
                  }
                }
              }
            }
          }
        },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, billingController.getAvailablePlans);

  // Get comprehensive billing information
  fastify.get('/info', {
    schema: {
      tags: ['Billing'],
      summary: 'Get comprehensive billing information',
      description: 'Retrieve billing info including current plan, payment methods, and recent payments',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                currentPlan: {
                  type: 'object',
                  properties: {
                    type: { type: 'string' },
                    name: { type: 'string' },
                    interval: { type: 'string' },
                    amount: {
                      type: 'object',
                      properties: {
                        value: { type: 'string' },
                        currency: { type: 'string' }
                      }
                    },
                    status: { type: 'string' }
                  }
                },
                paymentMethods: { type: 'array' },
                recentPayments: { type: 'array' },
                usage: {
                  type: 'object',
                  properties: {
                    emails: { type: 'integer' },
                    domains: { type: 'integer' },
                    webhooks: { type: 'integer' },
                    aliases: { type: 'integer' }
                  }
                },
                limits: {
                  type: 'object',
                  properties: {
                    emails: { type: 'integer' },
                    domains: { type: 'integer' },
                    webhooks: { type: 'integer' },
                    aliases: { type: 'integer' }
                  }
                }
              }
            }
          }
        },
        404: { $ref: 'ErrorResponse#' },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, billingController.getBillingInfo);

  // Create payment for plan upgrade
  fastify.post('/payment', {
    schema: {
      tags: ['Billing'],
      summary: 'Create payment for plan upgrade',
      description: 'Create a Mollie payment for upgrading to a paid plan',
      body: {
        type: 'object',
        properties: {
          planType: {
            type: 'string',
            enum: ['pro', 'enterprise'],
            description: 'The plan to upgrade to'
          },
          interval: {
            type: 'string',
            enum: ['monthly', 'yearly'],
            description: 'Billing interval'
          },
          successUrl: {
            type: 'string',
            description: 'URL to redirect to after successful payment'
          },
          cancelUrl: {
            type: 'string',
            description: 'URL to redirect to after cancelled payment'
          }
        },
        required: ['planType', 'interval']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            payment: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                mollieId: { type: 'string' },
                checkoutUrl: { type: 'string' },
                amount: {
                  type: 'object',
                  properties: {
                    value: { type: 'string' },
                    currency: { type: 'string' }
                  }
                }
              }
            }
          }
        },
        400: { $ref: 'ErrorResponse#' },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, billingController.createPayment);

  // Update user's plan
  fastify.put('/plan', {
    schema: {
      tags: ['Billing'],
      summary: 'Update user plan',
      description: 'Update the current user\'s subscription plan',
      body: {
        type: 'object',
        properties: {
          planType: { 
            type: 'string',
            enum: ['free', 'pro', 'enterprise'],
            description: 'The plan type to switch to'
          }
        },
        required: ['planType']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            user: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                name: { type: 'string', nullable: true },
                monthlyEmailLimit: { type: 'integer' },
                planType: { type: 'string' },
                currentMonthEmails: { type: 'integer' },
                verified: { type: 'boolean' }
              }
            }
          }
        },
        400: { $ref: 'ErrorResponse#' },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, billingController.updatePlan);

  // Get usage statistics
  fastify.get('/usage', {
    schema: {
      tags: ['Billing'],
      summary: 'Get usage statistics',
      description: 'Retrieve detailed usage statistics for the current user',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            usage: {
              type: 'object',
              properties: {
                emails: { type: 'integer' },
                domains: { type: 'integer' },
                webhooks: { type: 'integer' },
                aliases: { type: 'integer' }
              }
            },
            limits: {
              type: 'object',
              properties: {
                emails: { type: 'integer' },
                domains: { type: 'integer' },
                webhooks: { type: 'integer' },
                aliases: { type: 'integer' }
              }
            },
            percentages: {
              type: 'object',
              properties: {
                emails: { type: 'integer' },
                domains: { type: 'integer' },
                webhooks: { type: 'integer' },
                aliases: { type: 'integer' }
              }
            },
            plan: {
              type: 'object',
              properties: {
                name: { type: 'string' },
                type: { type: 'string' }
              }
            }
          }
        },
        404: { $ref: 'ErrorResponse#' },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, billingController.getUsageStats);

  // Check if user can perform an action based on limits
  fastify.get('/limits/check', {
    schema: {
      tags: ['Billing'],
      summary: 'Check action limits',
      description: 'Check if the user can perform a specific action based on their plan limits',
      querystring: {
        type: 'object',
        properties: {
          action: {
            type: 'string',
            enum: ['send_email', 'create_domain', 'create_webhook', 'create_alias'],
            description: 'The action to check'
          }
        },
        required: ['action']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            canPerform: { type: 'boolean' },
            reason: { type: 'string' },
            usage: {
              type: 'object',
              properties: {
                emails: { type: 'integer' },
                domains: { type: 'integer' },
                webhooks: { type: 'integer' },
                aliases: { type: 'integer' }
              }
            },
            limits: {
              type: 'object',
              properties: {
                emails: { type: 'integer' },
                domains: { type: 'integer' },
                webhooks: { type: 'integer' },
                aliases: { type: 'integer' }
              }
            }
          }
        },
        400: { $ref: 'ErrorResponse#' },
        404: { $ref: 'ErrorResponse#' },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, billingController.checkLimits);
};
