import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { userAuthMiddleware } from '../lib/auth.js'
import { ApiKeyService } from '../services/auth/api-key.service.js'

export async function apiKeyRoutes(fastify: FastifyInstance) {
  const apiKeyService = new ApiKeyService()

  // Generate new API key
  fastify.post('/api-keys', {
    preHandler: userAuthMiddleware,
    schema: {
      tags: ['API Keys'],
      summary: 'Generate new API key',
      description: 'Generate a new API key for the authenticated user.',
      body: {
        type: 'object',
        required: ['name'],
        properties: {
          name: { 
            type: 'string', 
            minLength: 1, 
            maxLength: 100,
            description: 'User-friendly name for the API key'
          }
        }
      },
      response: {
        '201': {
          description: 'API key generated successfully.',
          type: 'object',
          properties: {
            message: { type: 'string' },
            apiKey: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                keyPrefix: { type: 'string' },
                key: { type: 'string', description: 'Full API key - only shown once' },
                createdAt: { type: 'string', format: 'date-time' },
                lastUsedAt: { type: 'string', format: 'date-time', nullable: true }
              }
            }
          }
        },
        '400': { $ref: 'ErrorResponse#' },
        '401': { $ref: 'ErrorResponse#' },
        '500': { $ref: 'ErrorResponse#' }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    const { name } = request.body as { name: string }
    const user = (request as any).user

    const result = await apiKeyService.generateApiKey({
      name,
      userId: user.id
    })

    if (!result.success) {
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: result.error || 'Failed to generate API key'
      })
    }

    return reply.status(201).send({
      message: 'API key generated successfully',
      apiKey: result.apiKey
    })
  })

  // List user's API keys
  fastify.get('/api-keys', {
    preHandler: userAuthMiddleware,
    schema: {
      tags: ['API Keys'],
      summary: 'List API keys',
      description: 'List all API keys for the authenticated user.',
      response: {
        '200': {
          description: 'API keys retrieved successfully.',
          type: 'object',
          properties: {
            apiKeys: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  keyPrefix: { type: 'string' },
                  createdAt: { type: 'string', format: 'date-time' },
                  lastUsedAt: { type: 'string', format: 'date-time', nullable: true }
                }
              }
            }
          }
        },
        '401': { $ref: 'ErrorResponse#' },
        '500': { $ref: 'ErrorResponse#' }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    const user = (request as any).user

    const result = await apiKeyService.listApiKeys(user.id)

    if (!result.success) {
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: result.error || 'Failed to retrieve API keys'
      })
    }

    return reply.send({
      apiKeys: result.apiKeys || []
    })
  })

  // Revoke API key
  fastify.delete('/api-keys/:id', {
    preHandler: userAuthMiddleware,
    schema: {
      tags: ['API Keys'],
      summary: 'Revoke API key',
      description: 'Revoke (delete) an API key.',
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: 'API key ID' }
        }
      },
      response: {
        '200': {
          description: 'API key revoked successfully.',
          type: 'object',
          properties: {
            message: { type: 'string' }
          }
        },
        '404': { $ref: 'ErrorResponse#' },
        '401': { $ref: 'ErrorResponse#' },
        '500': { $ref: 'ErrorResponse#' }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    const { id } = request.params as { id: string }
    const user = (request as any).user

    const result = await apiKeyService.revokeApiKey(id, user.id)

    if (!result.success) {
      const statusCode = result.error === 'API key not found' ? 404 : 500
      return reply.status(statusCode).send({
        statusCode,
        error: statusCode === 404 ? 'Not Found' : 'Internal Server Error',
        message: result.error || 'Failed to revoke API key'
      })
    }

    return reply.send({
      message: 'API key revoked successfully'
    })
  })

  fastify.log.info('API key routes registered')
}
