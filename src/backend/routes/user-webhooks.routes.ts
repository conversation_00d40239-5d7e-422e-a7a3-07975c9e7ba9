import { FastifyPluginAsync } from 'fastify';
import { userOrApiKeyAuthMiddleware } from '../lib/auth.js';
import { WebhooksController } from '../controllers/user/webhooks.controller.js';
import { webhookSchemas } from '../schemas/user/webhook.schemas.js';

const webhooksController = new WebhooksController();

export const userWebhooksRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Get user's webhooks
  fastify.get('/webhooks', {
    preHandler: [userOrApiKeyAuthMiddleware],
    schema: {
      tags: ['User Webhooks'],
      summary: 'List user webhooks',
      description: 'Retrieves a list of webhooks for the authenticated user.',
      response: {
        200: {
          description: 'A list of user webhooks.',
          type: 'object',
          properties: {
            webhooks: {
              type: 'array',
              items: webhookSchemas.WebhookResponse
            },
            total: { type: 'integer' }
          }
        },
        401: { $ref: 'ErrorResponse#' },
        500: errorResponseSchema
      },
    }
  }, webhooksController.getWebhooks.bind(webhooksController));

  // Create new webhook
  fastify.post('/webhooks', {
    preHandler: [userOrApiKeyAuthMiddleware],
    schema: {
      tags: ['User Webhooks'],
      summary: 'Create a new webhook',
      description: 'Creates a new webhook for the authenticated user.',
      body: webhookSchemas.CreateWebhookRequest,
      response: {
        201: {
          description: 'Webhook created.',
          ...webhookSchemas.CreateWebhookResponse
        },
        400: errorResponseSchema,
        401: { $ref: 'ErrorResponse#' },
        409: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, webhooksController.createWebhook.bind(webhooksController));

  // Get specific webhook
  fastify.get('/webhooks/:webhookId', {
    preHandler: [userOrApiKeyAuthMiddleware],
    schema: {
      tags: ['User Webhooks'],
      summary: 'Get webhook by ID',
      params: webhookSchemas.WebhookIdParam,
      response: {
        200: webhookSchemas.WebhookDetailResponse,
        401: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, webhooksController.getWebhook.bind(webhooksController));

  // Update webhook
  fastify.put('/webhooks/:webhookId', {
    preHandler: [userOrApiKeyAuthMiddleware],
    schema: {
      tags: ['User Webhooks'],
      summary: 'Update webhook',
      params: webhookSchemas.WebhookIdParam,
      body: webhookSchemas.UpdateWebhookRequest,
      response: {
        200: webhookSchemas.UpdateWebhookResponse,
        400: errorResponseSchema,
        401: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        409: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, webhooksController.updateWebhook.bind(webhooksController));

  // Delete webhook
  fastify.delete('/webhooks/:webhookId', {
    preHandler: [userOrApiKeyAuthMiddleware],
    schema: {
      tags: ['User Webhooks'],
      summary: 'Delete webhook',
      params: webhookSchemas.WebhookIdParam,
      response: {
        200: webhookSchemas.DeleteWebhookResponse,
        400: errorResponseSchema,
        401: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, webhooksController.deleteWebhook.bind(webhooksController));

  // Verify webhook
  fastify.post('/webhooks/:webhookId/verify', {
    preHandler: [userOrApiKeyAuthMiddleware],
    schema: {
      tags: ['User Webhooks'],
      summary: 'Verify webhook',
      description: 'Sends a test request to verify the webhook endpoint.',
      params: webhookSchemas.WebhookIdParam,
      response: {
        200: webhookSchemas.VerifyWebhookResponse,
        401: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, webhooksController.verifyWebhook.bind(webhooksController));

  // Complete webhook verification with token
  fastify.post('/webhooks/:webhookId/verify/complete', {
    preHandler: [userOrApiKeyAuthMiddleware],
    schema: {
      tags: ['User Webhooks'],
      summary: 'Complete webhook verification',
      description: 'Completes webhook verification by providing the verification token.',
      params: webhookSchemas.WebhookIdParam,
      body: {
        type: 'object',
        required: ['verificationToken'],
        properties: {
          verificationToken: { type: 'string', description: 'The verification token received in the webhook payload' }
        }
      },
      response: {
        200: webhookSchemas.VerifyWebhookResponse,
        400: errorResponseSchema,
        401: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, webhooksController.completeWebhookVerification.bind(webhooksController));
};
