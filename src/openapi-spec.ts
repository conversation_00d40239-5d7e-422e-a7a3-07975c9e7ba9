import { OpenAPIV3 } from 'openapi-types';
import { allSchemas, allResponses } from './backend/schemas/openapi-schemas.js';
import { adminPaths, webhookPaths } from './backend/schemas/route-paths.js';

export const openApiSpecification: OpenAPIV3.Document = {
  openapi: '3.0.0',
  info: {
    title: 'Email Webhook API',
    version: '1.0.0',
    description: 'API for managing email domains, aliases, and webhooks.',
  },
  servers: [
    {
      url: 'http://localhost:3000',
      description: 'Development server',
    },
  ],
  components: {
    securitySchemes: {
      userApiKey: {
        type: 'apiKey',
        in: 'header',
        name: 'X-API-KEY',
        description: 'API Key for user authentication.',
      },
      adminAuth: {
        type: 'apiKey',
        in: 'cookie',
        name: 'admin_token',
        description: 'Admin authentication via HTTP-only cookie (admin_token). Set upon successful login to /admin/login.',
      },
    },
    schemas: allSchemas,
    responses: allResponses,
  },
  tags: [
    { name: 'User Domains', description: 'Endpoints for managing user-owned domains.' },
    { name: 'User Aliases', description: 'Endpoints for managing email aliases for a domain.' },
    { name: 'Admin Authentication', description: 'Endpoints for admin login and session management.'},
    { name: 'Admin Management', description: 'Endpoints for administrative tasks (requires admin login).' },
    { name: 'Webhook', description: 'Endpoints related to incoming email webhooks (internal or for testing).' },
  ],
  paths: {
    // Admin paths (now imported from route-paths.ts)
    ...adminPaths,
    
    // Webhook paths (now imported from route-paths.ts)
    ...webhookPaths,
    
    // Domain/Config paths will be auto-generated by the configRoutes
    // These are already handled by Fastify schema definitions in config.ts
    
    // Note: The user domain, alias, webhook, logs, and API key endpoints are automatically
    // generated by Fastify from the schema definitions in their respective route files.
    // This consolidation approach maintains the DRY principle by:
    // 1. Moving static admin/webhook routes to separate files
    // 2. Keeping dynamic business logic routes auto-generated from schemas
    // 3. Centralizing all schema definitions
    // 4. Supporting both cookie and API key authentication for user endpoints
  },
};

// Helper function to merge paths from route files
export function mergeRoutePaths(additionalPaths: Record<string, OpenAPIV3.PathItemObject>) {
  return {
    ...openApiSpecification,
    paths: {
      ...openApiSpecification.paths,
      ...additionalPaths,
    },
  };
}
