export interface Domain {
  id: string
  domain: string
  domainName?: string
  name?: string // Alias for domain for backward compatibility
  webhook?: Webhook // Webhook object with verification status
  active: boolean
  verified?: boolean
  isVerified?: boolean // Alternative field name used by API
  verificationStatus?: 'PENDING' | 'VERIFIED' | 'ACTIVE' | 'WARNING' | 'SUSPENDED' | 'FAILED'
  verificationToken?: string
  createdAt: string
  updatedAt: string
  aliases?: Alias[]
  postfix_configured?: boolean // From API response
  lastVerificationAttempt?: string
  verificationFailureCount?: number
  nextVerificationCheck?: string
  expectedTxtRecord?: string
}

export interface CreateDomainRequest {
  domain: string
  webhookId: string
  createCatchAll?: boolean
}

export interface UpdateDomainRequest {
  active?: boolean
  webhookId?: string
}

export interface DomainVerificationData {
  domain: string
  domainId: string
  verificationToken: string
  webhookVerified?: boolean
  webhookNeedsVerification?: boolean
  webhookId?: string
  webhookUrl?: string
  webhookName?: string
  shouldReloadAfterClose?: boolean
}

// Re-export related types
import type { Webhook } from './webhook'
import type { Alias } from './alias'