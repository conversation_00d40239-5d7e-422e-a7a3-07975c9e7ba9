export interface Webhook {
  id: string
  name: string
  url: string
  description?: string
  userId: string
  verified: boolean
  active: boolean
  createdAt: string
  updatedAt: string
  domains?: Domain[]
  aliases?: Alias[]
  domainCount?: number
  aliasCount?: number
  hasSecret?: boolean
}

export interface CreateWebhookRequest {
  name: string
  url: string
  description?: string
}

export interface UpdateWebhookRequest {
  name?: string
  url?: string
  description?: string
  active?: boolean
}

export interface WebhookVerificationData {
  webhookId: string
  webhookUrl: string
  webhookName?: string
}

export interface WebhookVerificationRequest {
  verificationToken: string
}

// Re-export related types
import type { Domain } from './domain'
import type { Alias } from './alias'