import { ref, computed } from 'vue'

// Shared state for metrics data
const metricsData = ref<any>(null)
const isLoading = ref(false)
const lastUpdated = ref<Date | null>(null)
const error = ref<string | null>(null)

// Single API call function
const fetchMetrics = async () => {
  if (isLoading.value) return // Prevent duplicate calls

  try {
    isLoading.value = true
    error.value = null
    
    const response = await fetch('/api/dashboard/metrics')
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const data = await response.json()
    metricsData.value = data
    lastUpdated.value = new Date()
    
    return data
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load metrics'
    console.error('Failed to load metrics:', err)
    throw err
  } finally {
    isLoading.value = false
  }
}

// Composable hook
export function useMetrics() {
  // Computed values for different use cases
  const counts = computed(() => ({
    domains: metricsData.value?.domains || 0,
    aliases: metricsData.value?.aliases || 0,
    webhooks: metricsData.value?.webhooks || 0
  }))

  const metrics = computed(() => ({
    emails_processed_24h: metricsData.value?.emails_processed_24h || 0,
    current_month_emails: metricsData.value?.monthly_usage || 0,
    monthly_email_limit: metricsData.value?.monthly_limit || 50,
    success_rate: metricsData.value?.success_rate || 0
  }))

  const quotaUsagePercent = computed(() => {
    const usage = metrics.value.current_month_emails
    const limit = metrics.value.monthly_email_limit
    return limit > 0 ? Math.round((usage / limit) * 100) : 0
  })

  const quotaColor = computed(() => {
    const percent = quotaUsagePercent.value
    return percent > 80 ? 'bg-error' : percent > 60 ? 'bg-warning' : 'bg-success'
  })

  const quotaTextColor = computed(() => {
    const percent = quotaUsagePercent.value
    return percent > 80 ? 'text-error' : percent > 60 ? 'text-warning' : 'text-base-content'
  })

  // Load metrics if not already loaded
  const loadMetrics = async () => {
    if (!metricsData.value || (lastUpdated.value && Date.now() - lastUpdated.value.getTime() > 30000)) {
      // Load if no data or data is older than 30 seconds
      await fetchMetrics()
    }
    return metricsData.value
  }

  // Force refresh - bypasses cache completely
  const refreshMetrics = async () => {
    // Reset lastUpdated to force a fresh fetch
    lastUpdated.value = null
    await fetchMetrics()
    return metricsData.value
  }

  return {
    // State
    metricsData: computed(() => metricsData.value),
    isLoading: computed(() => isLoading.value),
    lastUpdated: computed(() => lastUpdated.value),
    error: computed(() => error.value),
    
    // Computed data
    counts,
    metrics,
    quotaUsagePercent,
    quotaColor,
    quotaTextColor,
    
    // Methods
    loadMetrics,
    refreshMetrics
  }
}
