import { createRouter, createWebHistory } from 'vue-router'
import DomainsView from './components/dashboard/DomainsView.vue'
import AliasesView from './components/dashboard/AliasesView.vue'
import WebhooksView from './components/dashboard/WebhooksView.vue'
import LogsView from './components/dashboard/LogsView.vue'
import SettingsPage from './components/settings/SettingsPage.vue'
import LoginPage from './components/auth/LoginPage.vue'
import RegisterPage from './components/auth/RegisterPage.vue'
import LandingPage from './components/landing/LandingPage.vue'

const routes = [
  // Landing page (public)
  {
    path: '/',
    name: 'home',
    component: LandingPage,
    meta: { requiresAuth: false, layout: 'guest' }
  },
  // Auth routes (public)
  {
    path: '/login',
    name: 'login',
    component: LoginPage,
    meta: { requiresAuth: false, layout: 'auth' }
  },
  {
    path: '/register',
    name: 'register',
    component: RegisterPage,
    meta: { requiresAuth: false, layout: 'auth' }
  },
  // Dashboard routes (protected) - clean URLs
  {
    path: '/dashboard',
    redirect: '/domains'
  },
  {
    path: '/domains',
    name: 'domains',
    component: DomainsView,
    meta: { requiresAuth: true, layout: 'user' }
  },
  {
    path: '/aliases',
    name: 'aliases',
    component: AliasesView,
    meta: { requiresAuth: true, layout: 'user' }
  },
  {
    path: '/webhooks',
    name: 'webhooks',
    component: WebhooksView,
    meta: { requiresAuth: true, layout: 'user' }
  },
  {
    path: '/logs',
    name: 'logs',
    component: LogsView,
    meta: { requiresAuth: true, layout: 'user' }
  },
  {
    path: '/settings',
    name: 'settings',
    component: SettingsPage,
    meta: { requiresAuth: true, layout: 'user' }
  },
] as const

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Route guard to check authentication
router.beforeEach(async (to, from, next) => {
  const requiresAuth = to.meta.requiresAuth

  if (requiresAuth) {
    // For protected routes, check authentication via API call
    // since HttpOnly cookies can't be read by JavaScript
    try {
      const response = await fetch('/api/auth/check', {
        method: 'GET',
        credentials: 'include'
      })

      if (response.ok) {
        // User is authenticated
        next()
      } else {
        // User is not authenticated, redirect to login
        next('/login')
      }
    } catch (error) {
      // Network error or server down, redirect to login
      console.warn('Auth check failed:', error)
      next('/login')
    }
  } else if (!requiresAuth && (to.path === '/login' || to.path === '/register')) {
    // For login/register pages, check if user is already authenticated
    try {
      const response = await fetch('/api/auth/check', {
        method: 'GET',
        credentials: 'include'
      })

      if (response.ok) {
        // User is already authenticated, redirect to dashboard
        next('/domains')
      } else {
        // User is not authenticated, allow access to login/register
        next()
      }
    } catch (error) {
      // Network error, allow access to login/register
      console.warn('Network error:', error)
      next()
    }
  } else {
    // Public routes or other cases
    next()
  }
})

export default router
