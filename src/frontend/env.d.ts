/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<Record<string, unknown>, Record<string, unknown>, any>
  export default component
}

// Global types
declare global {
  interface Window {
    openModal: (type: string, data?: any) => void
    closeModal: () => void
    verifyWebhook: (webhookId: string) => void
    verifyDomain: (domainId: string) => void
    vueModal: {
      open: (type: string, data?: any) => void
      close: () => void
    }
    toast: {
      success: (message: string, duration?: number) => string
      error: (message: string, duration?: number) => string
      warning: (message: string, duration?: number) => string
      info: (message: string, duration?: number) => string
      show: (message: string, type: 'success' | 'error' | 'warning' | 'info', duration?: number) => string
      clearAll: () => void
    }
  }
}

export {}
