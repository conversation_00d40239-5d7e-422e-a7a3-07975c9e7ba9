<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useDataRefresh } from '@composables/useDataRefresh'
import { useAliasApi } from '@composables/useApi'
import { useConfirm } from '@composables/useConfirm'
import { useToast } from '@composables/useToast'
import { useMetrics } from '@composables/useMetrics'
import AliasesTable from './AliasesTable.vue'

// Router
const router = useRouter()

// Composables
const { deleteAlias } = useAliasApi()
const { confirmDelete } = useConfirm()
const { success, error } = useToast()
const { refreshMetrics } = useMetrics()

// State for aliases data
const aliases = ref([])
const isLoading = ref(true)

// Data refresh system
const { refreshState, updateData, triggerRefresh } = useDataRefresh()

// Load aliases data
const loadAliases = async () => {
  try {
    isLoading.value = true
    const response = await fetch('/api/aliases')
    const data = await response.json()
    aliases.value = data.aliases || []

    // Update global data store
    updateData('aliases', data.aliases || [])
  } catch (error) {
    console.error('Failed to load aliases:', error)
  } finally {
    isLoading.value = false
  }
}

// Watch for refresh triggers
watch(() => refreshState.aliases, () => {
  loadAliases()
})

// Handle view logs navigation
const handleViewLogs = (domainId: string, aliasId: string) => {
  // Navigate to logs tab and store domain and alias selection for LogsView to pick up
  sessionStorage.setItem('logsView_selectedDomain', domainId)
  sessionStorage.setItem('logsView_selectedAlias', aliasId)
  router.push('/logs')
}

// Handle alias deletion
const handleDeleteAlias = async (aliasId: string, aliasName: string) => {
  try {
    const confirmed = await confirmDelete(aliasName, 'alias')
    if (!confirmed) return

    await deleteAlias(aliasId)
    success(`Alias "${aliasName}" deleted successfully`)

    // Refresh the aliases list and trigger metrics update
    await loadAliases()
    triggerRefresh('aliases')

    // Force refresh metrics to update tab counts immediately
    setTimeout(async () => {
      try {
        await refreshMetrics()
        console.log('Metrics refreshed after alias deletion')
      } catch (error) {
        console.error('Failed to refresh metrics after alias deletion:', error)
      }
    }, 500)
  } catch (err) {
    console.error('Failed to delete alias:', err)
    error(err instanceof Error ? err.message : 'Failed to delete alias')
  }
}

onMounted(() => {
  loadAliases()
})

// Expose refresh method for parent components
defineExpose({
  refresh: loadAliases
})
</script>

<template>
  <div class="space-y-6">
    <!-- Loading state -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>

    <!-- Aliases table -->
    <AliasesTable
      v-else
      :aliases="aliases"
      @refresh="loadAliases"
      @view-logs="handleViewLogs"
      @delete-alias="handleDeleteAlias"
    />
  </div>
</template>
