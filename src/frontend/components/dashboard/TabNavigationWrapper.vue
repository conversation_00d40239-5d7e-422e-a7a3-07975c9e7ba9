<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import TabNavigation from './TabNavigation.vue'
import { useMetrics } from '@composables/useMetrics'
import { useDataRefresh } from '@composables/useDataRefresh'

// Use metrics system for reactive counts
const { counts: metricsCount, refreshMetrics } = useMetrics()
const { refreshState } = useDataRefresh()

// Use computed counts directly from metrics instead of local state
const counts = computed(() => {
  console.log('Computing counts from metrics:', metricsCount.value)
  return metricsCount.value
})

// Watch for data refresh triggers and update counts
watch([() => refreshState.domains, () => refreshState.aliases, () => refreshState.webhooks], async (newValues, oldValues) => {
  console.log('Data refresh triggered:', { newValues, oldValues })
  console.log('Refreshing metrics...')
  try {
    await refreshMetrics()
    console.log('Metrics refreshed successfully, new counts:', metricsCount.value)
  } catch (error) {
    console.error('Failed to refresh metrics:', error)
    // Retry once after a delay
    setTimeout(async () => {
      try {
        console.log('Retrying metrics refresh...')
        await refreshMetrics()
        console.log('Metrics refresh retry successful:', metricsCount.value)
      } catch (retryError) {
        console.error('Metrics refresh retry failed:', retryError)
      }
    }, 1000)
  }
}, { immediate: false, deep: true })

// Methods
const handleCreateAction = (actionType: string) => {
  // Use the existing global modal system
  if ((window as any).openModal) {
    (window as any).openModal(actionType)
  } else {
    console.warn('Modal system not available')
  }
}

onMounted(async () => {
  // Load initial metrics to get accurate counts
  console.log('TabNavigationWrapper mounted, loading metrics...')
  try {
    await refreshMetrics()
    console.log('Initial metrics loaded:', metricsCount.value)
  } catch (error) {
    console.error('Failed to load initial metrics:', error)
  }
})
</script>

<template>
  <TabNavigation
    :counts="counts"
    @create-action="handleCreateAction"
  />
</template>
