<script setup lang="ts">
import { ref, computed } from 'vue'
import { useWebhookApi } from '@composables/useApi'
import { useToast } from '@composables/useToast'

interface Props {
  webhookId: string
  webhookUrl: string
  webhookName: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  verified: []
  error: [message: string]
}>()

// State
const isVerifying = ref(false)
const verificationToken = ref('')
const verificationSent = ref(false)

// API
const { verifyWebhook, completeWebhookVerification } = useWebhookApi()
const { success, error } = useToast()

// Computed
const verificationCode = computed(() => {
  return props.webhookId.slice(-5).toUpperCase()
})

// Methods
const sendToken = async () => {
  try {
    isVerifying.value = true
    await verifyWebhook(props.webhookId)
    verificationSent.value = true
    success('Verification token sent to your webhook!')
  } catch (err) {
    console.error('Failed to send verification token:', err)
    error('Failed to send verification token')
    emit('error', 'Failed to send verification token')
  } finally {
    isVerifying.value = false
  }
}

const handleTokenInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value

  // Update the token value
  verificationToken.value = value

  // Auto-submit when 5 characters are entered
  if (value.length === 5 && !isVerifying.value) {
    verifyWebhookEndpoint()
  }
}

const verifyWebhookEndpoint = async () => {
  try {
    isVerifying.value = true
    await completeWebhookVerification(props.webhookId, verificationToken.value.trim())
    success('Webhook verified successfully!')
    emit('verified')
  } catch (err) {
    console.error('Webhook verification failed:', err)
    error('Webhook verification failed. Please check the token and try again.')
    emit('error', 'Webhook verification failed')
  } finally {
    isVerifying.value = false
  }
}
</script>

<template>
  <div class="space-y-4">
    <!-- Verification Steps -->
    <div class="space-y-3">
      <div class="flex items-start space-x-3">
        <div class="flex items-center justify-center w-6 h-6 text-sm font-medium text-white rounded-full bg-primary">
          1
        </div>
        <div class="flex-1">
          <p class="text-sm font-medium text-gray-900">Prepare your webhook endpoint</p>
          <p class="text-sm text-gray-600">
            Ensure your webhook endpoint at <code class="px-1 py-0.5 text-xs bg-gray-100 rounded">{{ webhookUrl }}</code> 
            is ready to receive POST requests and that you have access to its payload.
          </p>
        </div>
      </div>

      <div class="flex items-start space-x-3">
        <div class="flex items-center justify-center w-6 h-6 text-sm font-medium text-white rounded-full bg-primary">
          2
        </div>
        <div class="flex-1">
          <p class="text-sm font-medium text-gray-900">Send verification token</p>
          <p class="mb-2 text-sm text-gray-600">
            Click the button below to send a test payload to your webhook endpoint.
          </p>
          <button
            @click="sendToken"
            :disabled="isVerifying || verificationSent"
            class="btn btn-sm btn-primary"
            :class="{ 'loading': isVerifying }"
          >
            <span v-if="!isVerifying && !verificationSent">Send verification token</span>
            <span v-else-if="isVerifying">Sending...</span>
            <span v-else>Token sent ✓</span>
          </button>
        </div>
      </div>

      <div v-if="verificationSent" class="flex items-start space-x-3">
        <div class="flex items-center justify-center w-6 h-6 text-sm font-medium text-white rounded-full bg-warning">
          3
        </div>
        <div class="flex-1">
          <p class="text-sm font-medium text-gray-900">Enter verification token</p>
          <p class="mb-3 text-sm text-gray-600">
            Check your webhook logs for the <code class="px-1 py-0.5 text-xs bg-gray-100 rounded">verification_token</code> that was sent in the payload and enter it below.
          </p>
          <div class="flex gap-2 items-center">
            <input
              v-model="verificationToken"
              @input="handleTokenInput"
              type="text"
              placeholder="12345"
              class="input input-bordered input-sm font-mono text-center tracking-widest"
              style="width: 120px;"
              maxlength="5"
              autocomplete="off"
              spellcheck="false"
            />
            <button
              @click="verifyWebhookEndpoint"
              :disabled="isVerifying || !verificationToken.trim()"
              class="btn btn-sm btn-success"
              :class="{ 'loading': isVerifying }"
            >
              <span v-if="!isVerifying">Verify</span>
              <span v-else>Verifying...</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
