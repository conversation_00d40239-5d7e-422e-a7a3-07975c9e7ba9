<template>
  <div class="card bg-base-100">
    <div class="card-body pt-0">
      <h2 class="card-title mb-6">Billing</h2>

      <!-- Loading State -->
      <div v-if="isLoading" class="flex justify-center py-8">
        <span class="loading loading-spinner loading-md"></span>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="alert alert-error mb-6">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>{{ error }}</span>
        <button @click="loadBillingInfo" class="btn btn-sm btn-outline">
          Try again
        </button>
      </div>

      <!-- Billing Content -->
      <div v-else-if="billingInfo" class="space-y-6">
        
        <!-- Current Plan -->
        <div class="bg-primary/10 rounded-lg p-6">
          <div class="flex items-center justify-between mb-4">
            <div>
              <h3 class="text-lg font-semibold">Current plan</h3>
              <p class="text-base-content/70">{{ billingInfo.currentPlan.name }}</p>
            </div>
            <div class="text-right">
              <div v-if="billingInfo.currentPlan.amount" class="text-2xl font-bold">
                €{{ billingInfo.currentPlan.amount.value }}
                <span class="text-sm font-normal text-base-content/70">
                  /{{ billingInfo.currentPlan.interval }}
                </span>
              </div>
              <div v-else class="text-2xl font-bold text-success">
                Free
              </div>
            </div>
          </div>

          <!-- Plan Status -->
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <div 
                :class="[
                  'badge',
                  billingInfo.currentPlan.status === 'active' ? 'badge-success' : 
                  billingInfo.currentPlan.status === 'cancelled' ? 'badge-error' : 
                  'badge-warning'
                ]"
              >
                {{ billingInfo.currentPlan.status || 'active' }}
              </div>
              <span v-if="billingInfo.currentPlan.nextPaymentDate" class="text-sm text-base-content/70">
                Next payment: {{ formatDate(billingInfo.currentPlan.nextPaymentDate) }}
              </span>
            </div>
            
            <div class="space-x-2">
              <button 
                v-if="billingInfo.currentPlan.type === 'free'"
                @click="showUpgradeModal = true"
                class="btn btn-primary btn-sm"
              >
                Upgrade plan
              </button>
              <button 
                v-else
                @click="showUpgradeModal = true"
                class="btn btn-outline btn-sm"
              >
                Change plan
              </button>
            </div>
          </div>
        </div>

        <!-- Usage Overview -->
        <div class="bg-primary/10 rounded-lg p-6">
          <h3 class="text-lg font-semibold mb-4">Usage this month</h3>
          <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="text-center">
              <div class="text-2xl font-bold">
                {{ billingInfo.usage.emails }}
              </div>
              <div class="text-sm text-base-content/70">
                of {{ billingInfo.limits.emails }} emails
              </div>
              <div class="w-full bg-base-300 rounded-full h-2 mt-2">
                <div 
                  class="h-2 rounded-full transition-all duration-300"
                  :class="getUsageColor('emails')"
                  :style="{ width: getUsagePercentage('emails') + '%' }"
                ></div>
              </div>
            </div>
            
            <div class="text-center">
              <div class="text-2xl font-bold">{{ billingInfo.usage.domains }}</div>
              <div class="text-sm text-base-content/70">
                of {{ billingInfo.limits.domains }} domains
              </div>
              <div class="w-full bg-base-300 rounded-full h-2 mt-2">
                <div 
                  class="bg-primary h-2 rounded-full transition-all duration-300"
                  :style="{ width: getUsagePercentage('domains') + '%' }"
                ></div>
              </div>
            </div>
            
            <div class="text-center">
              <div class="text-2xl font-bold">{{ billingInfo.usage.webhooks }}</div>
              <div class="text-sm text-base-content/70">
                of {{ billingInfo.limits.webhooks }} webhooks
              </div>
              <div class="w-full bg-base-300 rounded-full h-2 mt-2">
                <div 
                  class="bg-primary h-2 rounded-full transition-all duration-300"
                  :style="{ width: getUsagePercentage('webhooks') + '%' }"
                ></div>
              </div>
            </div>
            
            <div class="text-center">
              <div class="text-2xl font-bold">{{ billingInfo.usage.aliases }}</div>
              <div class="text-sm text-base-content/70">
                of {{ billingInfo.limits.aliases }} aliases
              </div>
              <div class="w-full bg-base-300 rounded-full h-2 mt-2">
                <div 
                  class="bg-primary h-2 rounded-full transition-all duration-300"
                  :style="{ width: getUsagePercentage('aliases') + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Payment Methods -->
        <div class="bg-primary/10 rounded-lg p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">Payment methods</h3>
            <button 
              v-if="billingInfo.currentPlan.type !== 'free'"
              class="btn btn-outline btn-sm"
              @click="showAddPaymentMethodModal = true"
            >
              Add payment method
            </button>
          </div>

          <div v-if="billingInfo.paymentMethods.length > 0" class="space-y-3">
            <div 
              v-for="method in billingInfo.paymentMethods" 
              :key="method.id"
              class="flex items-center justify-between p-4 bg-base-100 rounded-lg"
            >
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary/20 rounded flex items-center justify-center">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" />
                  </svg>
                </div>
                <div>
                  <div class="font-medium">{{ method.description }}</div>
                  <div class="text-sm text-base-content/70">{{ method.type }}</div>
                </div>
                <div v-if="method.isDefault" class="badge badge-primary badge-sm">Default</div>
              </div>
              <button class="btn btn-ghost btn-sm text-error">Remove</button>
            </div>
          </div>
          
          <div v-else class="text-center py-8 text-base-content/70">
            <svg class="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
            <p>No payment methods added</p>
            <p class="text-sm">Add a payment method to upgrade your plan</p>
          </div>
        </div>

        <!-- Recent Payments -->
        <div class="bg-primary/10 rounded-lg p-6">
          <h3 class="text-lg font-semibold mb-4">Recent payments</h3>
          
          <div v-if="billingInfo.recentPayments.length > 0" class="space-y-3">
            <div 
              v-for="payment in billingInfo.recentPayments" 
              :key="payment.id"
              class="flex items-center justify-between p-4 bg-base-100 rounded-lg"
            >
              <div>
                <div class="font-medium">{{ payment.description }}</div>
                <div class="text-sm text-base-content/70">
                  {{ formatDate(payment.paidAt || payment.createdAt) }}
                </div>
              </div>
              <div class="text-right">
                <div class="font-medium">€{{ payment.amount.value }}</div>
                <div 
                  :class="[
                    'text-sm',
                    payment.status === 'paid' ? 'text-success' : 
                    payment.status === 'failed' ? 'text-error' : 
                    'text-warning'
                  ]"
                >
                  {{ payment.status }}
                </div>
              </div>
            </div>
          </div>
          
          <div v-else class="text-center py-8 text-base-content/70">
            <svg class="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <p>No payment history</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Plan Upgrade Modal -->
    <div v-if="showUpgradeModal" class="modal modal-open">
      <div class="modal-box max-w-4xl">
        <h3 class="text-lg font-bold mb-6">Choose your plan</h3>

        <!-- Plan Selection -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- Pro Plan -->
          <div class="card bg-primary/10 shadow-lg" :class="{ 'ring-2 ring-primary': selectedPlan === 'pro' }">
            <div class="card-body">
              <h4 class="card-title text-primary">Pro Plan</h4>
              <div class="text-3xl font-bold mb-2">
                €{{ selectedInterval === 'yearly' ? '99.99' : '9.99' }}
                <span class="text-sm font-normal text-base-content/70">
                  /{{ selectedInterval === 'yearly' ? 'year' : 'month' }}
                </span>
              </div>
              <ul class="space-y-2 mb-4">
                <li class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-success" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  Up to 1,000 emails per month
                </li>
                <li class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-success" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  Priority webhook delivery
                </li>
                <li class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-success" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  Email analytics
                </li>
                <li class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-success" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  Priority support
                </li>
              </ul>
              <button
                @click="selectedPlan = 'pro'"
                class="btn btn-primary w-full"
                :class="{ 'btn-outline': selectedPlan !== 'pro' }"
              >
                {{ selectedPlan === 'pro' ? 'Selected' : 'Select Pro' }}
              </button>
            </div>
          </div>

          <!-- Enterprise Plan -->
          <div class="card bg-primary/10 shadow-lg" :class="{ 'ring-2 ring-primary': selectedPlan === 'enterprise' }">
            <div class="card-body">
              <h4 class="card-title text-primary">Enterprise Plan</h4>
              <div class="text-3xl font-bold mb-2">
                €{{ selectedInterval === 'yearly' ? '499.99' : '49.99' }}
                <span class="text-sm font-normal text-base-content/70">
                  /{{ selectedInterval === 'yearly' ? 'year' : 'month' }}
                </span>
              </div>
              <ul class="space-y-2 mb-4">
                <li class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-success" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  Up to 10,000 emails per month
                </li>
                <li class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-success" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  Guaranteed webhook delivery
                </li>
                <li class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-success" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  Advanced analytics
                </li>
                <li class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-success" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  Dedicated support & SLA
                </li>
              </ul>
              <button
                @click="selectedPlan = 'enterprise'"
                class="btn btn-primary w-full"
                :class="{ 'btn-outline': selectedPlan !== 'enterprise' }"
              >
                {{ selectedPlan === 'enterprise' ? 'Selected' : 'Select Enterprise' }}
              </button>
            </div>
          </div>
        </div>

        <!-- Billing Interval -->
        <div class="mb-6">
          <h4 class="font-semibold mb-3">Billing interval</h4>
          <div class="flex space-x-4">
            <label class="flex items-center cursor-pointer">
              <input
                type="radio"
                v-model="selectedInterval"
                value="monthly"
                class="radio radio-primary mr-2"
              >
              <span>Monthly</span>
            </label>
            <label class="flex items-center cursor-pointer">
              <input
                type="radio"
                v-model="selectedInterval"
                value="yearly"
                class="radio radio-primary mr-2"
              >
              <span>Yearly</span>
              <span class="badge badge-success bg-primary/10 text-base-content badge-sm ml-2">Save 17%</span>
            </label>
          </div>
        </div>

        <div class="modal-action">
          <button @click="showUpgradeModal = false" class="btn btn-ghost">Cancel</button>
          <button
            @click="proceedToPayment"
            :disabled="!selectedPlan || isProcessingPayment"
            class="btn btn-primary"
          >
            {{ isProcessingPayment ? 'Processing...' : 'Proceed to payment' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Add Payment Method Modal -->
    <div v-if="showAddPaymentMethodModal" class="modal modal-open">
      <div class="modal-box">
        <h3 class="text-lg font-bold mb-4">Add payment method</h3>
        
        <div class="text-center py-8">
          <p class="text-base-content/70 mb-4">Payment method management coming soon!</p>
          <p class="text-sm text-base-content/50">This will provide secure payment processing.</p>
        </div>

        <div class="modal-action">
          <button @click="showAddPaymentMethodModal = false" class="btn">Close</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import type { BillingInfo } from '../../../shared/types/payment'

// State
const billingInfo = ref<BillingInfo | null>(null)
const isLoading = ref(true)
const error = ref<string | null>(null)
const showUpgradeModal = ref(false)
const showAddPaymentMethodModal = ref(false)

// Plan selection state
const selectedPlan = ref<'pro' | 'enterprise' | null>(null)
const selectedInterval = ref<'monthly' | 'yearly'>('monthly')
const isProcessingPayment = ref(false)

// Methods
const loadBillingInfo = async () => {
  try {
    isLoading.value = true
    error.value = null
    
    const response = await fetch('/api/billing/info', {
      credentials: 'include'
    })
    
    if (!response.ok) {
      throw new Error('Failed to load billing information')
    }
    
    const data = await response.json()
    if (data.success) {
      billingInfo.value = data.data
    } else {
      throw new Error(data.message || 'Invalid response format')
    }
  } catch (err: any) {
    error.value = err.message
    console.error('Failed to load billing info:', err)
  } finally {
    isLoading.value = false
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getUsagePercentage = (type: keyof BillingInfo['usage']) => {
  if (!billingInfo.value) return 0
  const usage = billingInfo.value.usage[type]
  const limit = billingInfo.value.limits[type]
  return Math.min((usage / limit) * 100, 100)
}

const getUsageColor = (type: keyof BillingInfo['usage']) => {
  const percentage = getUsagePercentage(type)
  if (percentage >= 90) return 'text-error bg-error'
  if (percentage >= 75) return 'text-warning bg-warning'
  return 'text-success bg-success'
}

const proceedToPayment = async () => {
  if (!selectedPlan.value) return

  try {
    isProcessingPayment.value = true

    const response = await fetch('/api/billing/payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({
        planType: selectedPlan.value,
        interval: selectedInterval.value
      })
    })

    if (!response.ok) {
      throw new Error('Failed to create payment')
    }

    const data = await response.json()
    if (data.success && data.payment.checkoutUrl) {
      // Redirect to Mollie checkout
      window.location.href = data.payment.checkoutUrl
    } else {
      throw new Error('Invalid payment response')
    }
  } catch (err: any) {
    console.error('Payment creation failed:', err)
    alert('Failed to create payment. Please try again.')
  } finally {
    isProcessingPayment.value = false
  }
}

onMounted(() => {
  loadBillingInfo()
})
</script>
