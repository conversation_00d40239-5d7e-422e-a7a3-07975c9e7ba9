<template>
  <div class="flex items-center justify-center min-h-screen px-4 py-12 bg-base-200 sm:px-6 lg:px-8">
      <div class="w-full max-w-md space-y-8">
        <div>
          <h2 class="mt-6 text-3xl font-extrabold text-center text-base-content">
            Sign in to your account
          </h2>
          <p class="mt-2 text-sm text-center text-base-content/70">
            Or
            <router-link to="/register" class="font-medium text-primary hover:text-primary/80">
              create a new account
            </router-link>
          </p>
        </div>

        <!-- Error <PERSON> -->
        <div v-if="error" class="alert alert-error">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{{ error }}</span>
        </div>

        <form class="mt-8 space-y-6" @submit.prevent="handleSubmit">
          <div class="space-y-4">
            <div class="form-control">
              <label for="email" class="label">
                <span class="label-text">Email address</span>
              </label>
              <input
                id="email"
                v-model="form.email"
                name="email"
                type="email"
                autocomplete="email"
                required
                class="input input-bordered w-full"
                placeholder="Email address"
              >
            </div>
            <div class="form-control">
              <label for="password" class="label">
                <span class="label-text">Password</span>
              </label>
              <input
                id="password"
                v-model="form.password"
                name="password"
                type="password"
                autocomplete="current-password"
                required
                class="input input-bordered w-full"
                placeholder="Password"
              >
            </div>
          </div>

          <div class="flex items-center justify-between">
            <div class="form-control">
              <label class="label cursor-pointer">
                <input
                  id="remember-me"
                  v-model="form.rememberMe"
                  name="remember-me"
                  type="checkbox"
                  class="checkbox checkbox-primary"
                >
                <span class="label-text ml-2">Remember me</span>
              </label>
            </div>

            <div class="text-sm">
              <a href="#" class="link link-primary">
                Forgot your password?
              </a>
            </div>
          </div>

          <div>
            <button
              type="submit"
              :disabled="loading"
              class="btn btn-primary w-full"
              :class="{ 'loading': loading }"
            >
              <span v-if="loading" class="loading loading-spinner loading-sm"></span>
              {{ loading ? 'Signing in...' : 'Sign in' }}
            </button>
          </div>
        </form>
      </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

// State
const loading = ref(false)
const error = ref('')

const form = reactive({
  email: '',
  password: '',
  rememberMe: false
})

// Handle form submission
const handleSubmit = async () => {
  loading.value = true
  error.value = ''

  try {
    const response = await fetch('/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies to receive user_token
      body: JSON.stringify({
        email: form.email,
        password: form.password
      }),
    })
    
    const result = await response.json()
    
    if (response.ok) {
      // Success - redirect to dashboard
      window.location.href = '/domains'
    } else {
      // Show error
      error.value = result.error || 'Login failed. Please try again.'
    }
  } catch (err) {
    console.error('Login error:', err)
    error.value = 'An unexpected error occurred. Please try again.'
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* Login page specific styles */
</style>
