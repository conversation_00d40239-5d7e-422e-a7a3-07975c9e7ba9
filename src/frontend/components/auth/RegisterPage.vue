<template>
  <div class="flex items-center justify-center min-h-screen px-4 py-12 bg-base-200 sm:px-6 lg:px-8">
      <div class="w-full max-w-md space-y-8">
        <div>
          <h2 class="mt-6 text-3xl font-extrabold text-center text-base-content">
            Create your account
          </h2>
          <p class="mt-2 text-sm text-center text-base-content/70">
            Or
            <router-link to="/login" class="font-medium text-primary hover:text-primary/80">
              sign in to your existing account
            </router-link>
          </p>
        </div>

        <!-- Error <PERSON> -->
        <div v-if="error" class="alert alert-error">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{{ error }}</span>
        </div>

        <!-- Success Alert -->
        <div v-if="success" class="alert alert-success">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{{ success }}</span>
        </div>

        <form class="mt-8 space-y-6" @submit.prevent="handleSubmit">
          <div class="space-y-4">
            <div class="form-control">
              <label for="name" class="label">
                <span class="label-text">Full Name (Optional)</span>
              </label>
              <input
                id="name"
                v-model="form.name"
                name="name"
                type="text"
                autocomplete="name"
                class="input input-bordered w-full"
                placeholder="Enter your full name"
              >
            </div>
            
            <div class="form-control">
              <label for="email" class="label">
                <span class="label-text">Email Address</span>
              </label>
              <input
                id="email"
                v-model="form.email"
                name="email"
                type="email"
                autocomplete="email"
                required
                class="input input-bordered w-full"
                placeholder="Enter your email address"
              >
            </div>

            <div class="form-control">
              <label for="password" class="label">
                <span class="label-text">Password</span>
              </label>
              <input
                id="password"
                v-model="form.password"
                name="password"
                type="password"
                autocomplete="new-password"
                required
                class="input input-bordered w-full"
                placeholder="Create a password (min. 8 characters)"
                minlength="8"
              >
            </div>
          </div>

          <div class="form-control">
            <label class="label cursor-pointer justify-start">
              <input
                id="terms"
                v-model="form.acceptTerms"
                name="terms"
                type="checkbox"
                required
                class="checkbox checkbox-primary"
              >
              <span class="label-text ml-2">
                I agree to the
                <a href="#" class="link link-primary">Terms of Service</a>
                and
                <a href="#" class="link link-primary">Privacy Policy</a>
              </span>
            </label>
          </div>

          <div>
            <button
              type="submit"
              :disabled="loading"
              class="btn btn-primary w-full"
              :class="{ 'loading': loading }"
            >
              <span v-if="loading" class="loading loading-spinner loading-sm"></span>
              {{ loading ? 'Creating account...' : 'Create account' }}
            </button>
          </div>
        </form>
      </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

// State
const loading = ref(false)
const error = ref('')
const success = ref('')

const form = reactive({
  name: '',
  email: '',
  password: '',
  acceptTerms: false
})

// Handle form submission
const handleSubmit = async () => {
  loading.value = true
  error.value = ''
  success.value = ''

  try {
    const response = await fetch('/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies to receive user_token
      body: JSON.stringify({
        name: form.name || undefined,
        email: form.email,
        password: form.password
      }),
    })
    
    const result = await response.json()
    
    if (response.ok) {
      // Success - redirect to dashboard
      success.value = 'Account created successfully! Redirecting...'
      setTimeout(() => {
        window.location.href = '/domains'
      }, 1500)
    } else {
      // Show error
      error.value = result.error || 'Registration failed. Please try again.'
    }
  } catch (err) {
    console.error('Registration error:', err)
    error.value = 'An unexpected error occurred. Please try again.'
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* Register page specific styles */
</style>
