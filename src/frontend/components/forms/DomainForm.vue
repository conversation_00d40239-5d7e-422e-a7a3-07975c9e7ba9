<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useForm } from '@composables/useForm'
import { useDomainApi } from '@composables/useApi'
import { useDataRefresh } from '@composables/useDataRefresh'
import { useMetrics } from '@composables/useMetrics'
import WebhookSelector from './WebhookSelector.vue'
import type { CreateDomainRequest, Webhook } from '@types'

interface Props {
  initialData?: Partial<CreateDomainRequest & { preselectedWebhook?: string }>
}

const props = withDefaults(defineProps<Props>(), {
  initialData: () => ({})
})

const emit = defineEmits<{
  success: [domain: any]
  cancel: []
}>()

// Form setup
const { values, errors, isSubmitting, isValid, setFieldValue, handleSubmit } = useForm<CreateDomainRequest>({
  domain: props.initialData.domain || '',
  webhookId: props.initialData.preselectedWebhook || props.initialData.webhookId || '',
  createCatchAll: true // Always create catch-all, no user choice needed
}, [
  { name: 'domain', label: 'Domain', type: 'text', required: true },
  { name: 'webhookId', label: 'Webhook', type: 'select', required: true }
])

// API setup
const { createDomain } = useDomainApi()
const { refreshWithSuccess } = useDataRefresh()
const { refreshMetrics } = useMetrics()

// Methods
const onWebhookCreated = (webhook: Webhook) => {
  console.log('New webhook created:', webhook)
  // The WebhookSelector component will automatically select the new webhook
}

const onSubmit = async () => {
  await handleSubmit(async (formData) => {
    const result = await createDomain(formData) as any
    emit('success', result.domain)

    // Update data reactively - refresh both domains and aliases if catch-all was created
    refreshWithSuccess('Domain created successfully!', 'domains')

    // If catch-all alias was created, also refresh aliases count
    if (formData.createCatchAll && result.alias) {
      const { triggerRefresh } = useDataRefresh()
      triggerRefresh('aliases')
    }

    // Force refresh metrics to update tab counts immediately
    setTimeout(async () => {
      try {
        await refreshMetrics()
        console.log('Metrics refreshed after domain creation')
      } catch (error) {
        console.error('Failed to refresh metrics after domain creation:', error)
      }
    }, 500)

    // Auto-open verification steps modal
    setTimeout(() => {
      const modalData: any = {
        domain: result.domain.domain,
        domainId: result.domain.id,
        verificationToken: result.domain.verificationToken,
        webhookVerified: result.webhook?.verified
      };

      console.log('Domain creation result:', result);
      console.log('Webhook from result:', result.webhook);

      // Include webhook verification data if webhook needs verification
      if (result.webhook && !result.webhook.verified && result.webhook.id) {
        console.log('Adding webhook verification data to modal');
        modalData.webhookNeedsVerification = true;
        modalData.webhookId = result.webhook.id;
        modalData.webhookUrl = result.webhook.url;
        modalData.webhookName = result.webhook.name;
      } else {
        console.log('Webhook verification not needed:', {
          hasWebhook: !!result.webhook,
          webhookVerified: result.webhook?.verified,
          webhookId: result.webhook?.id
        });
      }

      console.log('Final modal data:', modalData);
      (window as any).openModal('domain-verification', modalData);
    }, 500);
  })
}

onMounted(() => {
  // WebhookSelector component handles loading webhooks
})
</script>

<template>
  <form @submit.prevent="onSubmit" class="space-y-6">
    <!-- Domain Name -->
    <div class="form-control">
      <label class="label">
        <span class="label-text">Domain name</span>
      </label>
      <input
        :value="values.domain"
        @input="setFieldValue('domain', ($event.target as HTMLInputElement).value)"
        type="text"
        placeholder="example.com"
        class="w-full input input-bordered"
        :class="{ 'input-error': errors.domain }"
        required
      />
      <div v-if="errors.domain" class="label">
        <span class="label-text-alt text-error">{{ errors.domain }}</span>
      </div>
      <div class="label">
        <span class="label-text-alt text-xs">Enter your domain without 'www' prefix</span>
      </div>
    </div>

    <!-- Webhook Selection -->
    <WebhookSelector
      :model-value="values.webhookId"
      @update:model-value="setFieldValue('webhookId', $event)"
      @webhook-created="onWebhookCreated"
      context="create-domain"
      required
    />
    <div v-if="errors.webhookId" class="label">
      <span class="label-text-alt text-error">{{ errors.webhookId }}</span>
    </div>

    <!-- Form-level errors -->
    <div v-if="errors._form" class="alert alert-error">
      <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 stroke-current shrink-0" fill="none" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <span>{{ errors._form }}</span>
    </div>

    <!-- Form Actions -->
    <div class="modal-action">
      <button
        type="button"
        @click="emit('cancel')"
        class="btn btn-ghost"
      >
        Cancel
      </button>
      <button
        type="submit"
        class="btn btn-primary"
        :disabled="!isValid || isSubmitting"
        :class="{ loading: isSubmitting }"
      >
        {{ isSubmitting ? 'Creating...' : 'Create domain' }}
      </button>
    </div>
  </form>
</template>