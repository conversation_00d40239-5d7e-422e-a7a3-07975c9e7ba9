<template>
  <div class="bg-base-100 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <div class="badge badge-accent mb-4">How it works</div>
        <h2 class="text-3xl lg:text-4xl font-bold text-base-content mb-4">
          From e-mail to webhook in 3 simple steps
        </h2>
        <p class="text-xl text-base-content/70 max-w-3xl mx-auto">
          Get your email-to-webhook service running in minutes with our simple setup process
        </p>
      </div>
      
      <!-- Steps -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
        <!-- Step 1 -->
        <div class="relative">
          <div class="flex flex-col items-center text-center">
            <div class="w-16 h-16 bg-primary text-primary-content rounded-full flex items-center justify-center text-2xl font-bold mb-6">
              1
            </div>
            <h3 class="text-xl font-semibold mb-4">Register your domain</h3>
            <p class="text-base-content/70 mb-6">
              Add your domain through our API or dashboard. We'll provide the DNS records you need to configure.
            </p>
            <div class="mockup-code bg-base-300 text-left text-sm max-w-xs">
              <pre class="text-base-content"><code>curl -X POST /api/domains \
  -d '{
    "domain": "example.com",
    "webhookUrl": "https://app.com/hook"
  }'</code></pre>
            </div>
          </div>
          
          <!-- Connector Line -->
          <div class="hidden lg:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-primary to-secondary transform translate-x-6"></div>
        </div>
        
        <!-- Step 2 -->
        <div class="relative">
          <div class="flex flex-col items-center text-center">
            <div class="w-16 h-16 bg-secondary text-secondary-content rounded-full flex items-center justify-center text-2xl font-bold mb-6">
              2
            </div>
            <h3 class="text-xl font-semibold mb-4">Configure DNS records</h3>
            <p class="text-base-content/70 mb-6">
              Update your domain's MX and TXT records. Our system automatically verifies ownership and configuration.
            </p>
            <div class="space-y-2 text-sm">
              <div class="bg-base-300 rounded p-3 font-mono text-base-content">
                <div class="text-primary font-semibold">MX</div>
                <div>@ → mw.xadi.eu (10)</div>
              </div>
              <div class="bg-base-300 rounded p-3 font-mono text-base-content">
                <div class="text-secondary font-semibold">TXT</div>
                <div>@ → verification-token</div>
              </div>
            </div>
          </div>
          
          <!-- Connector Line -->
          <div class="hidden lg:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-secondary to-accent transform translate-x-6"></div>
        </div>
        
        <!-- Step 3 -->
        <div class="relative">
          <div class="flex flex-col items-center text-center">
            <div class="w-16 h-16 bg-accent text-accent-content rounded-full flex items-center justify-center text-2xl font-bold mb-6">
              3
            </div>
            <h3 class="text-xl font-semibold mb-4">Receive webhooks</h3>
            <p class="text-base-content/70 mb-6">
              Start receiving emails as HTTP POST requests. Each email is parsed and delivered to your webhook URL.
            </p>
            <div class="mockup-code bg-base-300 text-left text-sm max-w-xs">
              <pre class="text-base-content"><code>POST /your-webhook
{
  "from": "<EMAIL>",
  "subject": "Hello World",
  "body": "Email content...",
  "timestamp": "2024-01-01T12:00:00Z"
}</code></pre>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Example Integration -->
      <div class="mt-20">
        <div class="bg-base-200 rounded-2xl p-8 lg:p-12">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
              <h3 class="text-2xl font-bold mb-4">Example integration</h3>
              <p class="text-base-content/70 mb-6">
                Here's how easy it is to handle incoming emails in your application:
              </p>
              <div class="space-y-4">
                <div class="flex items-center space-x-3">
                  <div class="w-2 h-2 bg-success rounded-full"></div>
                  <span>Parse email headers and content</span>
                </div>
                <div class="flex items-center space-x-3">
                  <div class="w-2 h-2 bg-success rounded-full"></div>
                  <span>Handle attachments (base64 encoded)</span>
                </div>
                <div class="flex items-center space-x-3">
                  <div class="w-2 h-2 bg-success rounded-full"></div>
                  <span>Process in your preferred language</span>
                </div>
              </div>
            </div>
            
            <div class="mockup-code bg-base-300">
              <pre class="text-base-content"><code>// Express.js example
app.post('/webhook', (req, res) => {
  const { from, subject, body, attachments } = req.body;
  
  // Process the email
  console.log(`New email from: ${from}`);
  console.log(`Subject: ${subject}`);
  
  // Handle attachments
  attachments.forEach(attachment => {
    const buffer = Buffer.from(attachment.content, 'base64');
    // Save or process attachment
  });
  
  res.status(200).send('OK');
});</code></pre>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Performance Stats -->
      <div class="mt-16 text-center">
        <div class="bg-gradient-to-r from-primary/10 to-secondary/10 rounded-2xl p-8">
          <h3 class="text-xl font-semibold mb-6">Trusted performance</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div class="text-3xl font-bold text-primary mb-2">99.9%</div>
              <div class="text-base-content/70">Webhook delivery success rate</div>
            </div>
            <div>
              <div class="text-3xl font-bold text-secondary mb-2">&lt;20s</div>
              <div class="text-base-content/70">Average processing time</div>
            </div>
            <div>
              <div class="text-3xl font-bold text-accent mb-2">24/7</div>
              <div class="text-base-content/70">Monitoring & support</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// How it works section explaining the process
</script>
