<template>
  <div id="features" class="bg-base-200 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <div class="badge badge-primary mb-4">Features</div>
        <h2 class="text-3xl lg:text-4xl font-bold text-base-content mb-4">
          Everything you need for email automation
        </h2>
        <p class="text-xl text-base-content/70 max-w-3xl mx-auto">
          Built for developers and automation enthusiasts who need reliable email processing 
          with seamless integration capabilities.
        </p>
      </div>
      
      <!-- Features Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- GDPR Compliance -->
        <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow">
          <div class="card-body">
            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
              </svg>
            </div>
            <h3 class="card-title text-lg mb-2">EU-sovereign</h3>
            <p class="text-base-content/70">
              EU-based infrastructure, owned and operated. Automatic data expiration after 30 days. Privacy-first by design, performance uncompromised.
            </p>
          </div>
        </div>
        
        <!-- Real-time Processing -->
        <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow">
          <div class="card-body">
            <div class="w-12 h-12 bg-secondary/10 rounded-lg flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
              </svg>
            </div>
            <h3 class="card-title text-lg mb-2">Automate</h3>
            <p class="text-base-content/70">
              Advanced email parsing with instant webhook delivery. Built-in queueing and automatic retries ensure nothing gets lost.
            </p>
          </div>
        </div>
        
        <!-- Multi-domain Support -->
        <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow">
          <div class="card-body">
            <div class="w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"/>
              </svg>
            </div>
            <h3 class="card-title text-lg mb-2">Connect</h3>
            <p class="text-base-content/70">
              Integrates with N8N, Zapier, Make.com, and any webhook-enabled tool. Includes DNS verification and unlimited domains & aliases.
            </p>
          </div>
        </div>
        
        <!-- Developer API -->
        <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow">
          <div class="card-body">
            <div class="w-12 h-12 bg-info/10 rounded-lg flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
              </svg>
            </div>
            <h3 class="card-title text-lg mb-2">Developer API</h3>
            <p class="text-base-content/70">
              REST API for managing domains, aliases, webhooks, and delivery metrics. Fully documented with OpenAPI spec.
            </p>
          </div>
        </div>
        
        <!-- Email Parsing -->
        <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow">
          <div class="card-body">
            <div class="w-12 h-12 bg-success/10 rounded-lg flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
              </svg>
            </div>
            <h3 class="card-title text-lg mb-2">Full email parsing</h3>
            <p class="text-base-content/70">
              Extract headers, HTML/plain content, and attachments (roadmap). Compatible with all standard formats and encodings.
            </p>
          </div>
        </div>
        
        <!-- Monitoring & Analytics -->
        <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow">
          <div class="card-body">
            <div class="w-12 h-12 bg-warning/10 rounded-lg flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2 2z"/>
              </svg>
            </div>
            <div class="flex items-center gap-2 mb-2">
              <h3 class="card-title text-lg">Monitoring & analytics</h3>
              <div class="badge badge-warning badge-sm">Roadmap</div>
            </div>
            <p class="text-base-content/70">
              Track deliveries, failed webhooks, and system events in real time. Audit logging for transparency and compliance.
            </p>
          </div>
        </div>
      </div>
      
      <!-- Bottom CTA -->
      <div class="text-center mt-16">
        <div class="bg-base-100 rounded-2xl p-8 shadow-lg inline-block">
          <h3 class="text-xl font-semibold mb-4">Ready to get started?</h3>
          <p class="text-base-content/70 mb-6">
            Set up your first domain in under 5 minutes
          </p>
          <router-link to="/register" class="btn btn-primary">
            Get started free
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Features section showcasing key capabilities
</script>
