import { describe, test, expect } from '@jest/globals';
import { EmailParser } from '../../../src/backend/services/email-parser';

describe('EmailParser', () => {
  describe('parseToWebhookPayload', () => {
    test('should parse basic email with all required fields', async () => {
      const rawEmail = `From: <PERSON> <<EMAIL>>
To: <EMAIL>
Subject: Test Subject
Date: Wed, 12 Jun 2025 10:30:00 +0000
Message-ID: <<EMAIL>>

This is the plain text content.`;

      const result = await EmailParser.parseToWebhookPayload(rawEmail, 'testdomain.com');

      expect(result.message.sender.name).toBe('<PERSON>');
      expect(result.message.sender.email).toBe('<EMAIL>');
      expect(result.message.recipient.email).toBe('<EMAIL>');
      expect(result.message.subject).toBe('Test Subject');
      expect(result.message.content.text).toBe('This is the plain text content.');
      expect(result.envelope.messageId).toBe('<<EMAIL>>');
      expect(result.envelope.processed.domain).toBe('testdomain.com');
    });

    test('should parse email with HTML content', async () => {
      const rawEmail = `From: <EMAIL>
To: <EMAIL>
Subject: HTML Test
Content-Type: text/html; charset=utf-8

<html><body><h1>HTML Content</h1><p>This is HTML.</p></body></html>`;

      const result = await EmailParser.parseToWebhookPayload(rawEmail, 'testdomain.com');

      expect(result.message.content.html).toContain('<h1>HTML Content</h1>');
      expect(result.message.content.html).toContain('<p>This is HTML.</p>');
    });

    test('should handle email without sender name', async () => {
      const rawEmail = `From: <EMAIL>
To: <EMAIL>
Subject: No Name Test

Content without sender name.`;

      const result = await EmailParser.parseToWebhookPayload(rawEmail, 'testdomain.com');

      expect(result.message.sender.name).toBeNull();
      expect(result.message.sender.email).toBe('<EMAIL>');
    });

    test('should handle email without recipient name', async () => {
      const rawEmail = `From: John Sender <<EMAIL>>
To: <EMAIL>
Subject: No Recipient Name

Content.`;

      const result = await EmailParser.parseToWebhookPayload(rawEmail, 'testdomain.com');

      expect(result.message.recipient.name).toBeNull();
      expect(result.message.recipient.email).toBe('<EMAIL>');
    });

    test('should handle multiple recipients correctly', async () => {
      const rawEmail = `From: <EMAIL>
To: <EMAIL>, <EMAIL>
Cc: <EMAIL>, <EMAIL>
Subject: Multiple Recipients

Content for multiple recipients.`;

      const result = await EmailParser.parseToWebhookPayload(rawEmail, 'testdomain.com');

      // Should pick first recipient as primary
      expect(result.message.recipient.email).toBe('<EMAIL>');
      
      // Should capture all recipients in envelope
      expect(result.envelope.allRecipients.to).toEqual(['<EMAIL>', '<EMAIL>']);
      expect(result.envelope.allRecipients.cc).toEqual(['<EMAIL>', '<EMAIL>']);
    });

    test('should handle emails with attachments', async () => {
      // Create a minimal email with attachment simulation
      const rawEmail = `From: <EMAIL>
To: <EMAIL>
Subject: Email with Attachment
Content-Type: multipart/mixed; boundary="boundary123"

--boundary123
Content-Type: text/plain

This email has an attachment.

--boundary123
Content-Type: text/plain; name="test.txt"
Content-Disposition: attachment; filename="test.txt"

File content here
--boundary123--`;

      const result = await EmailParser.parseToWebhookPayload(rawEmail, 'testdomain.com');

      expect(result.message.attachments).toBeDefined();
      // Note: Actual attachment parsing depends on mailparser's behavior
      // This test verifies the structure is correct
    });

    test('should handle missing subject', async () => {
      const rawEmail = `From: <EMAIL>
To: <EMAIL>

Content without subject.`;

      const result = await EmailParser.parseToWebhookPayload(rawEmail, 'testdomain.com');

      expect(result.message.subject).toBeNull();
      expect(result.message.content.text).toBe('Content without subject.');
    });

    test('should generate message ID when missing', async () => {
      const rawEmail = `From: <EMAIL>
To: <EMAIL>
Subject: No Message ID

Content.`;

      const result = await EmailParser.parseToWebhookPayload(rawEmail, 'testdomain.com');

      expect(result.envelope.messageId).toMatch(/^generated-\d+-/);
    });

    test('should extract custom headers', async () => {
      const rawEmail = `From: <EMAIL>
To: <EMAIL>
Subject: Custom Headers
X-Custom-Header: CustomValue
X-Mailer: TestMailer

Content with custom headers.`;

      const result = await EmailParser.parseToWebhookPayload(rawEmail, 'testdomain.com');

      expect(result.envelope.xMailer).toBe('TestMailer');
      expect(result.envelope.headers['x-custom-header']).toBe('CustomValue');
    });

    test('should handle Buffer input', async () => {
      const emailString = `From: <EMAIL>
To: <EMAIL>
Subject: Buffer Test

Buffer content.`;
      
      const rawEmail = Buffer.from(emailString, 'utf8');

      const result = await EmailParser.parseToWebhookPayload(rawEmail, 'testdomain.com');

      expect(result.message.sender.email).toBe('<EMAIL>');
      expect(result.message.recipient.email).toBe('<EMAIL>');
      expect(result.message.subject).toBe('Buffer Test');
    });

    test('should set correct timestamp and domain in processed info', async () => {
      const rawEmail = `From: <EMAIL>
To: <EMAIL>
Subject: Processing Info

Content.`;

      const result = await EmailParser.parseToWebhookPayload(rawEmail, 'customdomain.com');

      expect(result.envelope.processed.domain).toBe('customdomain.com');
      expect(result.envelope.processed.timestamp).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);
      expect(result.envelope.processed.originalSize).toBeGreaterThan(0);
    });
  });

  describe('extractDomainFromEmail', () => {
    test('should extract domain from simple email', () => {
      const result = EmailParser.extractDomainFromEmail('<EMAIL>');
      expect(result).toBe('example.com');
    });

    test('should extract domain and convert to lowercase', () => {
      const result = EmailParser.extractDomainFromEmail('<EMAIL>');
      expect(result).toBe('example.com');
    });

    test('should handle subdomain', () => {
      const result = EmailParser.extractDomainFromEmail('<EMAIL>');
      expect(result).toBe('mail.example.com');
    });

    test('should return empty string for invalid email', () => {
      const result = EmailParser.extractDomainFromEmail('invalid-email');
      expect(result).toBe('');
    });

    test('should handle email with plus addressing', () => {
      const result = EmailParser.extractDomainFromEmail('<EMAIL>');
      expect(result).toBe('example.com');
    });

    test('should handle empty string', () => {
      const result = EmailParser.extractDomainFromEmail('');
      expect(result).toBe('');
    });
  });
});
