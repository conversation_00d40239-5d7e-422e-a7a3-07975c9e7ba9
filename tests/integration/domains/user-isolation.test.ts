import { describe, test, expect } from '@jest/globals';
import {
  setupTestDatabase,
  prisma,
  createTestUser,
  createTestWebhook,
  createTestDomain,
  createTestAlias,
} from '../../setup/test-db-setup';

describe('User Data Isolation Tests', () => {
  setupTestDatabase();

  test('should ensure complete user data isolation across all entities', async () => {
    // Create two users exactly like the legacy test
    const user1 = await createTestUser({
      email: '<EMAIL>',
      name: 'Test User 1',
      verified: true,
    });

    const user2 = await createTestUser({
      email: '<EMAIL>',
      name: 'Test User 2',
      verified: true,
    });

    // Create webhooks for each user
    const user1Webhook = await createTestWebhook(user1.id, {
      name: 'User 1 Webhook',
      url: 'https://user1.example.com/webhook',
    });

    const user2Webhook = await createTestWebhook(user2.id, {
      name: 'User 2 Webhook',
      url: 'https://user2.example.com/webhook',
    });

    // Create domains for each user
    const user1Domain = await createTestDomain(user1.id, user1Webhook.id, {
      domain: 'user1-test.com',
      verified: true,
      verificationStatus: 'VERIFIED',
      active: true,
    });

    const user2Domain = await createTestDomain(user2.id, user2Webhook.id, {
      domain: 'user2-test.com',
      verified: true,
      verificationStatus: 'VERIFIED',
      active: true,
    });

    // Test 1: User 1 can only see their own domains
    const user1Domains = await prisma.domain.findMany({
      where: { userId: user1.id },
    });

    expect(user1Domains).toHaveLength(1);
    expect(user1Domains[0].domain).toBe('user1-test.com');
    expect(user1Domains[0].userId).toBe(user1.id);

    // Test 2: User 2 can only see their own domains
    const user2Domains = await prisma.domain.findMany({
      where: { userId: user2.id },
    });

    expect(user2Domains).toHaveLength(1);
    expect(user2Domains[0].domain).toBe('user2-test.com');
    expect(user2Domains[0].userId).toBe(user2.id);

    // Test 3: User 1 cannot access User 2's domain by cross-query
    const user1CannotSeeUser2Domain = await prisma.domain.findMany({
      where: {
        userId: user1.id,
        domain: 'user2-test.com', // Try to access user2's domain
      },
    });

    expect(user1CannotSeeUser2Domain).toHaveLength(0);

    // Test 4: User 2 cannot access User 1's domain by cross-query
    const user2CannotSeeUser1Domain = await prisma.domain.findMany({
      where: {
        userId: user2.id,
        domain: 'user1-test.com', // Try to access user1's domain
      },
    });

    expect(user2CannotSeeUser1Domain).toHaveLength(0);

    // Test 5: User 1 can only see their own webhooks
    const user1Webhooks = await prisma.webhook.findMany({
      where: { userId: user1.id },
    });

    expect(user1Webhooks).toHaveLength(1);
    expect(user1Webhooks[0].name).toBe('User 1 Webhook');
    expect(user1Webhooks[0].userId).toBe(user1.id);

    // Test 6: User 2 can only see their own webhooks
    const user2Webhooks = await prisma.webhook.findMany({
      where: { userId: user2.id },
    });

    expect(user2Webhooks).toHaveLength(1);
    expect(user2Webhooks[0].name).toBe('User 2 Webhook');
    expect(user2Webhooks[0].userId).toBe(user2.id);

    // Test 7: User 1 cannot access User 2's webhook by ID query
    const user1CannotSeeUser2Webhook = await prisma.webhook.findFirst({
      where: {
        id: user2Webhook.id,
        userId: user1.id, // User 1 tries to access user 2's webhook
      },
    });

    expect(user1CannotSeeUser2Webhook).toBeNull();

    // Test 8: User 2 cannot access User 1's webhook by ID query
    const user2CannotSeeUser1Webhook = await prisma.webhook.findFirst({
      where: {
        id: user1Webhook.id,
        userId: user2.id, // User 2 tries to access user 1's webhook
      },
    });

    expect(user2CannotSeeUser1Webhook).toBeNull();
  });

  test('should isolate alias data between users', async () => {
    // Create users
    const user1 = await createTestUser({ email: '<EMAIL>' });
    const user2 = await createTestUser({ email: '<EMAIL>' });

    // Create webhooks and domains
    const user1Webhook = await createTestWebhook(user1.id);
    const user2Webhook = await createTestWebhook(user2.id);
    
    const user1Domain = await createTestDomain(user1.id, user1Webhook.id, {
      domain: 'alias1-test.com',
    });
    const user2Domain = await createTestDomain(user2.id, user2Webhook.id, {
      domain: 'alias2-test.com',
    });

    // Create aliases for each user
    const user1Alias = await createTestAlias(user1Domain.id, user1Webhook.id, {
      email: '<EMAIL>',
    });

    const user2Alias = await createTestAlias(user2Domain.id, user2Webhook.id, {
      email: '<EMAIL>',
    });

    // Test alias isolation through domain relationships
    const user1Aliases = await prisma.alias.findMany({
      where: {
        domain: {
          userId: user1.id,
        },
      },
    });

    const user2Aliases = await prisma.alias.findMany({
      where: {
        domain: {
          userId: user2.id,
        },
      },
    });

    expect(user1Aliases).toHaveLength(1);
    expect(user1Aliases[0].email).toBe('<EMAIL>');

    expect(user2Aliases).toHaveLength(1);
    expect(user2Aliases[0].email).toBe('<EMAIL>');

    // Test cross-user alias access prevention
    const user1CannotSeeUser2Aliases = await prisma.alias.findMany({
      where: {
        id: user2Alias.id,
        domain: {
          userId: user1.id,
        },
      },
    });

    expect(user1CannotSeeUser2Aliases).toHaveLength(0);
  });

  test('should enforce isolation in complex queries with includes', async () => {
    // Create users with complete data sets
    const user1 = await createTestUser({ email: '<EMAIL>' });
    const user2 = await createTestUser({ email: '<EMAIL>' });

    // Create multiple webhooks per user
    const user1Webhook1 = await createTestWebhook(user1.id, { name: 'User 1 Webhook 1' });
    const user1Webhook2 = await createTestWebhook(user1.id, { name: 'User 1 Webhook 2' });
    const user2Webhook1 = await createTestWebhook(user2.id, { name: 'User 2 Webhook 1' });

    // Create domains with webhooks
    const user1Domain = await createTestDomain(user1.id, user1Webhook1.id, {
      domain: 'complex1-test.com',
    });
    const user2Domain = await createTestDomain(user2.id, user2Webhook1.id, {
      domain: 'complex2-test.com',
    });

    // Create aliases with different webhooks
    await createTestAlias(user1Domain.id, user1Webhook2.id, {
      email: '<EMAIL>',
    });
    await createTestAlias(user2Domain.id, user2Webhook1.id, {
      email: '<EMAIL>',
    });

    // Test complex query with includes for user 1
    const user1CompleteData = await prisma.user.findUnique({
      where: { id: user1.id },
      include: {
        webhooks: true,
        domains: {
          include: {
            aliases: true,
            webhook: true,
          },
        },
      },
    });

    expect(user1CompleteData.webhooks).toHaveLength(2);
    expect(user1CompleteData.domains).toHaveLength(1);
    expect(user1CompleteData.domains[0].domain).toBe('complex1-test.com');
    expect(user1CompleteData.domains[0].aliases).toHaveLength(1);
    expect(user1CompleteData.domains[0].aliases[0].email).toBe('<EMAIL>');

    // Test complex query with includes for user 2
    const user2CompleteData = await prisma.user.findUnique({
      where: { id: user2.id },
      include: {
        webhooks: true,
        domains: {
          include: {
            aliases: true,
            webhook: true,
          },
        },
      },
    });

    expect(user2CompleteData.webhooks).toHaveLength(1);
    expect(user2CompleteData.domains).toHaveLength(1);
    expect(user2CompleteData.domains[0].domain).toBe('complex2-test.com');
    expect(user2CompleteData.domains[0].aliases).toHaveLength(1);
    expect(user2CompleteData.domains[0].aliases[0].email).toBe('<EMAIL>');

    // Ensure no cross-contamination
    const allWebhookNames1 = user1CompleteData.webhooks.map(w => w.name);
    const allWebhookNames2 = user2CompleteData.webhooks.map(w => w.name);

    expect(allWebhookNames1).toContain('User 1 Webhook 1');
    expect(allWebhookNames1).toContain('User 1 Webhook 2');
    expect(allWebhookNames1).not.toContain('User 2 Webhook 1');

    expect(allWebhookNames2).toContain('User 2 Webhook 1');
    expect(allWebhookNames2).not.toContain('User 1 Webhook 1');
    expect(allWebhookNames2).not.toContain('User 1 Webhook 2');
  });

  test('should simulate dashboard metrics isolation', async () => {
    // Create users with different amounts of data (simulating dashboard metrics)
    const user1 = await createTestUser({ email: '<EMAIL>' });
    const user2 = await createTestUser({ email: '<EMAIL>' });

    // User 1: 2 webhooks, 2 domains
    const user1Webhook1 = await createTestWebhook(user1.id, { name: 'User 1 Primary' });
    const user1Webhook2 = await createTestWebhook(user1.id, { name: 'User 1 Secondary' });
    
    await createTestDomain(user1.id, user1Webhook1.id, { domain: 'primary1.com' });
    await createTestDomain(user1.id, user1Webhook2.id, { domain: 'secondary1.com' });

    // User 2: 1 webhook, 1 domain
    const user2Webhook1 = await createTestWebhook(user2.id, { name: 'User 2 Only' });
    await createTestDomain(user2.id, user2Webhook1.id, { domain: 'only2.com' });

    // Simulate dashboard metrics queries
    const user1Metrics = {
      domains: await prisma.domain.count({ where: { userId: user1.id } }),
      webhooks: await prisma.webhook.count({ where: { userId: user1.id } }),
      aliases: await prisma.alias.count({
        where: { domain: { userId: user1.id } },
      }),
    };

    const user2Metrics = {
      domains: await prisma.domain.count({ where: { userId: user2.id } }),
      webhooks: await prisma.webhook.count({ where: { userId: user2.id } }),
      aliases: await prisma.alias.count({
        where: { domain: { userId: user2.id } },
      }),
    };

    // Verify metrics are user-specific
    expect(user1Metrics.domains).toBe(2);
    expect(user1Metrics.webhooks).toBe(2);
    expect(user1Metrics.aliases).toBe(0); // No aliases created for user 1

    expect(user2Metrics.domains).toBe(1);
    expect(user2Metrics.webhooks).toBe(1);
    expect(user2Metrics.aliases).toBe(0); // No aliases created for user 2

    // Verify totals don't leak between users
    expect(user1Metrics.domains + user2Metrics.domains).toBe(3); // Total domains
    expect(user1Metrics.webhooks + user2Metrics.webhooks).toBe(3); // Total webhooks
  });

  test('should prevent access through indirect relationships', async () => {
    // Create test scenario where one user tries to access another's data
    // through relationship traversal
    const user1 = await createTestUser({ email: '<EMAIL>' });
    const user2 = await createTestUser({ email: '<EMAIL>' });

    const user1Webhook = await createTestWebhook(user1.id);
    const user2Webhook = await createTestWebhook(user2.id);

    const user1Domain = await createTestDomain(user1.id, user1Webhook.id, {
      domain: 'indirect1.com',
    });
    const user2Domain = await createTestDomain(user2.id, user2Webhook.id, {
      domain: 'indirect2.com',
    });

    // Try to access other user's data through webhook relationships
    const user1WebhookWithDomains = await prisma.webhook.findFirst({
      where: {
        id: user1Webhook.id,
        userId: user1.id,
      },
      include: {
        domains: true,
      },
    });

    expect(user1WebhookWithDomains.domains).toHaveLength(1);
    expect(user1WebhookWithDomains.domains[0].domain).toBe('indirect1.com');

    // Verify user 1 cannot see user 2's webhook even with includes
    const crossUserWebhookAccess = await prisma.webhook.findFirst({
      where: {
        id: user2Webhook.id,
        userId: user1.id, // User 1 trying to access user 2's webhook
      },
      include: {
        domains: true,
      },
    });

    expect(crossUserWebhookAccess).toBeNull();

    // Try to access domains through webhook traversal with wrong user context
    const domainsThroughWrongUser = await prisma.domain.findMany({
      where: {
        webhookId: user2Webhook.id,
        userId: user1.id, // User 1 trying to access domains with user 2's webhook
      },
    });

    expect(domainsThroughWrongUser).toHaveLength(0);
  });

  test('should handle user deletion and cascade isolation correctly', async () => {
    // Create two users with full data sets
    const user1 = await createTestUser({ email: '<EMAIL>' });
    const user2 = await createTestUser({ email: '<EMAIL>' });

    // Create data for both users
    const user1Webhook = await createTestWebhook(user1.id);
    const user2Webhook = await createTestWebhook(user2.id);

    const user1Domain = await createTestDomain(user1.id, user1Webhook.id);
    const user2Domain = await createTestDomain(user2.id, user2Webhook.id);

    await createTestAlias(user1Domain.id, user1Webhook.id);
    await createTestAlias(user2Domain.id, user2Webhook.id);

    // Verify both users have data
    expect(await prisma.user.findUnique({ where: { id: user1.id } })).toBeTruthy();
    expect(await prisma.user.findUnique({ where: { id: user2.id } })).toBeTruthy();
    expect(await prisma.webhook.count({ where: { userId: user1.id } })).toBe(1);
    expect(await prisma.webhook.count({ where: { userId: user2.id } })).toBe(1);

    // Delete user 1 (manually clean up aliases first due to FK constraints)
    await prisma.alias.deleteMany({
      where: { domain: { userId: user1.id } }
    });
    await prisma.user.delete({ where: { id: user1.id } });

    // Verify user 1's data is gone but user 2's data remains
    expect(await prisma.user.findUnique({ where: { id: user1.id } })).toBeNull();
    expect(await prisma.user.findUnique({ where: { id: user2.id } })).toBeTruthy();

    expect(await prisma.webhook.count({ where: { userId: user1.id } })).toBe(0);
    expect(await prisma.webhook.count({ where: { userId: user2.id } })).toBe(1);

    expect(await prisma.domain.count({ where: { userId: user1.id } })).toBe(0);
    expect(await prisma.domain.count({ where: { userId: user2.id } })).toBe(1);

    // Verify user 2 can still access their data normally
    const user2Data = await prisma.user.findUnique({
      where: { id: user2.id },
      include: {
        webhooks: true,
        domains: {
          include: {
            aliases: true,
          },
        },
      },
    });

    expect(user2Data).toBeTruthy();
    expect(user2Data.webhooks).toHaveLength(1);
    expect(user2Data.domains).toHaveLength(1);
    expect(user2Data.domains[0].aliases).toHaveLength(1);
  });
});
