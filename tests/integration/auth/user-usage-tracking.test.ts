import { describe, test, expect } from '@jest/globals';
import {
  setupTestDatabase,
  prisma,
  createTestUser,
  createTestWebhook,
  createTestDomain,
} from '../../setup/test-db-setup';
import { PlanConfigService } from '../../../src/backend/services/billing/plan-config.service';
import { UserAuthService } from '../../../src/backend/services/auth/user-auth.service';

describe('User Usage Tracking & Limits', () => {
  setupTestDatabase();

  test('should track user email usage correctly', async () => {
    // Create test user with specific usage settings
    const testUser = await createTestUser({
      email: '<EMAIL>',
      name: 'Usage Test User',
      currentMonthEmails: 0,
      monthlyEmailLimit: 50,
      verified: true,
    });

    expect(testUser.currentMonthEmails).toBe(0);
    expect(testUser.monthlyEmailLimit).toBe(50);

    // Simulate email processing (increment usage)
    const updatedUser = await prisma.user.update({
      where: { id: testUser.id },
      data: {
        currentMonthEmails: { increment: 5 }
      }
    });

    expect(updatedUser.currentMonthEmails).toBe(5);
    expect(updatedUser.monthlyEmailLimit).toBe(50);

    // Simulate processing more emails
    const finalUser = await prisma.user.update({
      where: { id: testUser.id },
      data: {
        currentMonthEmails: { increment: 10 }
      }
    });

    expect(finalUser.currentMonthEmails).toBe(15);
  });

  test('should enforce monthly email limits correctly', async () => {
    // Create user with specific limit
    const testUser = await createTestUser({
      email: '<EMAIL>',
      currentMonthEmails: 0,
      monthlyEmailLimit: 50,
    });

    // Test near limit
    await prisma.user.update({
      where: { id: testUser.id },
      data: { currentMonthEmails: 49 }
    });

    const nearLimitUser = await prisma.user.findUnique({
      where: { id: testUser.id },
      select: { currentMonthEmails: true, monthlyEmailLimit: true }
    });

    const canProcessMore = nearLimitUser.currentMonthEmails < nearLimitUser.monthlyEmailLimit;
    expect(canProcessMore).toBe(true);
    expect(nearLimitUser.currentMonthEmails).toBe(49);
    expect(nearLimitUser.monthlyEmailLimit).toBe(50);

    // Test at limit
    await prisma.user.update({
      where: { id: testUser.id },
      data: { currentMonthEmails: 50 }
    });

    const atLimitUser = await prisma.user.findUnique({
      where: { id: testUser.id },
      select: { currentMonthEmails: true, monthlyEmailLimit: true }
    });

    const canProcessAtLimit = atLimitUser.currentMonthEmails < atLimitUser.monthlyEmailLimit;
    expect(canProcessAtLimit).toBe(false);
    expect(atLimitUser.currentMonthEmails).toBe(50);
    expect(atLimitUser.monthlyEmailLimit).toBe(50);
  });

  test('should handle different plan types and limits', async () => {
    // Create users with different plan types
    const freeUser = await createTestUser({
      email: '<EMAIL>',
      planType: 'free',
      monthlyEmailLimit: 50,
      currentMonthEmails: 0,
    });

    const proUser = await createTestUser({
      email: '<EMAIL>',
      planType: 'pro',
      monthlyEmailLimit: 1000,
      currentMonthEmails: 0,
    });

    expect(freeUser.planType).toBe('free');
    expect(freeUser.monthlyEmailLimit).toBe(50);

    expect(proUser.planType).toBe('pro');
    expect(proUser.monthlyEmailLimit).toBe(1000);
  });

  test('should maintain accurate usage across multiple increments', async () => {
    const testUser = await createTestUser({
      email: '<EMAIL>',
      currentMonthEmails: 0,
      monthlyEmailLimit: 100,
    });

    // Simulate multiple email processing events
    const increments = [3, 7, 2, 8, 5];
    let expectedTotal = 0;

    for (const increment of increments) {
      await prisma.user.update({
        where: { id: testUser.id },
        data: {
          currentMonthEmails: { increment }
        }
      });
      expectedTotal += increment;

      const currentUser = await prisma.user.findUnique({
        where: { id: testUser.id },
        select: { currentMonthEmails: true }
      });

      expect(currentUser.currentMonthEmails).toBe(expectedTotal);
    }

    expect(expectedTotal).toBe(25); // 3+7+2+8+5
  });

  test('should simulate complete user workflow with data isolation', async () => {
    // This test replicates the exact workflow from the legacy test-phase2.js
    
    // Step 1: Create users
    const user1 = await createTestUser({
      email: '<EMAIL>',
      name: 'Test User 1',
      verified: true,
    });

    const user2 = await createTestUser({
      email: '<EMAIL>',
      name: 'Test User 2',
      verified: true,
    });

    expect(user1.email).toBe('<EMAIL>');
    expect(user2.email).toBe('<EMAIL>');

    // Step 2: Create webhooks for each user
    const webhook1 = await createTestWebhook(user1.id, {
      name: 'User 1 Webhook',
      url: 'https://user1.example.com/webhook',
    });

    const webhook2 = await createTestWebhook(user2.id, {
      name: 'User 2 Webhook',
      url: 'https://user2.example.com/webhook',
    });

    expect(webhook1.userId).toBe(user1.id);
    expect(webhook2.userId).toBe(user2.id);

    // Step 3: Create domains for each user
    const domain1 = await createTestDomain(user1.id, webhook1.id, {
      domain: 'user1-test.com',
      verified: true,
      verificationStatus: 'VERIFIED',
    });

    const domain2 = await createTestDomain(user2.id, webhook2.id, {
      domain: 'user2-test.com',
      verified: true,
      verificationStatus: 'VERIFIED',
    });

    expect(domain1.domain).toBe('user1-test.com');
    expect(domain2.domain).toBe('user2-test.com');

    // Step 4: Test user isolation - each user should only see their own domains
    const user1Domains = await prisma.domain.findMany({
      where: { userId: user1.id },
      include: { user: true, webhook: true }
    });

    const user2Domains = await prisma.domain.findMany({
      where: { userId: user2.id },
      include: { user: true, webhook: true }
    });

    expect(user1Domains).toHaveLength(1);
    expect(user2Domains).toHaveLength(1);
    expect(user1Domains[0].domain).toBe('user1-test.com');
    expect(user2Domains[0].domain).toBe('user2-test.com');
    expect(user1Domains[0].user.email).toBe('<EMAIL>');
    expect(user2Domains[0].user.email).toBe('<EMAIL>');

    // Step 5: Test usage tracking like in legacy test
    const initialUser1 = await prisma.user.findUnique({
      where: { id: user1.id },
      select: { currentMonthEmails: true, monthlyEmailLimit: true }
    });

    expect(initialUser1.currentMonthEmails).toBe(0);
    expect(initialUser1.monthlyEmailLimit).toBe(50); // Default from createTestUser

    // Simulate email processing (increment usage)
    await prisma.user.update({
      where: { id: user1.id },
      data: {
        currentMonthEmails: { increment: 5 }
      }
    });

    const updatedUser1 = await prisma.user.findUnique({
      where: { id: user1.id },
      select: { currentMonthEmails: true, monthlyEmailLimit: true }
    });

    expect(updatedUser1.currentMonthEmails).toBe(5);
    expect(updatedUser1.monthlyEmailLimit).toBe(50);

    // Step 6: Test monthly limit checking like in legacy test
    // Set user to near limit
    await prisma.user.update({
      where: { id: user1.id },
      data: { currentMonthEmails: 49 }
    });

    const nearLimitUser = await prisma.user.findUnique({
      where: { id: user1.id },
      select: { currentMonthEmails: true, monthlyEmailLimit: true }
    });

    const canProcessMore = nearLimitUser.currentMonthEmails < nearLimitUser.monthlyEmailLimit;
    expect(canProcessMore).toBe(true);

    // Set to limit
    await prisma.user.update({
      where: { id: user1.id },
      data: { currentMonthEmails: 50 }
    });

    const atLimitUser = await prisma.user.findUnique({
      where: { id: user1.id },
      select: { currentMonthEmails: true, monthlyEmailLimit: true }
    });

    const canProcessAtLimit = atLimitUser.currentMonthEmails < atLimitUser.monthlyEmailLimit;
    expect(canProcessAtLimit).toBe(false);

    // Verify user 2 is unaffected by user 1's usage
    const user2Check = await prisma.user.findUnique({
      where: { id: user2.id },
      select: { currentMonthEmails: true, monthlyEmailLimit: true }
    });

    expect(user2Check.currentMonthEmails).toBe(0); // User 2 should be unaffected
    expect(user2Check.monthlyEmailLimit).toBe(50);
  });

  test('should handle usage tracking edge cases', async () => {
    const testUser = await createTestUser({
      email: '<EMAIL>',
      currentMonthEmails: 0,
      monthlyEmailLimit: 10,
    });

    // Test large increment
    await prisma.user.update({
      where: { id: testUser.id },
      data: { currentMonthEmails: { increment: 100 } }
    });

    const overLimitUser = await prisma.user.findUnique({
      where: { id: testUser.id },
      select: { currentMonthEmails: true, monthlyEmailLimit: true }
    });

    expect(overLimitUser.currentMonthEmails).toBe(100);
    expect(overLimitUser.currentMonthEmails).toBeGreaterThan(overLimitUser.monthlyEmailLimit);

    // Reset to zero
    await prisma.user.update({
      where: { id: testUser.id },
      data: { currentMonthEmails: 0 }
    });

    const resetUser = await prisma.user.findUnique({
      where: { id: testUser.id },
      select: { currentMonthEmails: true }
    });

    expect(resetUser.currentMonthEmails).toBe(0);
  });

  test('should use configurable default limits from plan service', async () => {
    const defaultLimit = PlanConfigService.getDefaultEmailLimit();
    expect(defaultLimit).toBe(50); // From env config

    const testUser = await createTestUser({
      email: '<EMAIL>',
      // Don't specify monthlyEmailLimit, should use default
    });

    expect(testUser.monthlyEmailLimit).toBe(50); // Default from schema
  });

  test('should enforce plan-specific limits', async () => {
    const userAuthService = new UserAuthService();

    // Test free plan limits
    const freeUser = await createTestUser({
      email: '<EMAIL>',
      planType: 'free',
      monthlyEmailLimit: 50,
    });

    const freePlanInfo = await userAuthService.getUserPlanInfo(freeUser.id);
    expect(freePlanInfo?.limits.emails).toBe(50);
    expect(freePlanInfo?.limits.domains).toBe(3);
    expect(freePlanInfo?.limits.webhooks).toBe(2);
    expect(freePlanInfo?.limits.aliases).toBe(10);

    // Test pro plan limits
    const proUser = await createTestUser({
      email: '<EMAIL>',
      planType: 'pro',
      monthlyEmailLimit: 1000,
    });

    const proPlanInfo = await userAuthService.getUserPlanInfo(proUser.id);
    expect(proPlanInfo?.limits.emails).toBe(1000);
    expect(proPlanInfo?.limits.domains).toBe(10);
    expect(proPlanInfo?.limits.webhooks).toBe(5);
    expect(proPlanInfo?.limits.aliases).toBe(50);
  });

  test('should track usage across different resource types', async () => {
    const userAuthService = new UserAuthService();

    const testUser = await createTestUser({
      email: '<EMAIL>',
      currentMonthEmails: 25,
    });

    // Create webhook
    const webhook = await createTestWebhook(testUser.id, {
      name: 'Test Webhook',
      url: 'https://example.com/webhook',
    });

    // Create domain
    const domain = await createTestDomain(testUser.id, webhook.id, {
      domain: 'test-multi.com',
    });

    // Create alias
    await prisma.alias.create({
      data: {
        email: '<EMAIL>',
        domainId: domain.id,
        webhookId: webhook.id,
      }
    });

    const planInfo = await userAuthService.getUserPlanInfo(testUser.id);
    expect(planInfo?.usage.emails).toBe(25);
    expect(planInfo?.usage.domains).toBe(1);
    expect(planInfo?.usage.webhooks).toBe(1);
    expect(planInfo?.usage.aliases).toBe(1);
  });

  test('should calculate usage percentages correctly', async () => {
    const testUser = await createTestUser({
      email: '<EMAIL>',
      currentMonthEmails: 25,
      monthlyEmailLimit: 50,
    });

    // 25/50 = 50%
    const usagePercentage = Math.round((25 / 50) * 100);
    expect(usagePercentage).toBe(50);

    // Test near limit
    await prisma.user.update({
      where: { id: testUser.id },
      data: { currentMonthEmails: 48 }
    });

    const nearLimitPercentage = Math.round((48 / 50) * 100);
    expect(nearLimitPercentage).toBe(96);
  });
});
