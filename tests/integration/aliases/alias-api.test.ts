import { describe, test, expect } from '@jest/globals';
import { 
  prisma, 
  setupTestDatabase, 
  createTestUser, 
  createTestWebhook, 
  createTestDomain,
  createTestAlias 
} from '../../setup/test-db-setup';

setupTestDatabase();

describe('Alias API Operations', () => {
  test('should create aliases with proper relationships', async () => {
    const user = await createTestUser({ email: '<EMAIL>' });
    const webhook = await createTestWebhook(user.id, {
      name: 'Test Webhook',
      url: 'https://test.example.com/webhook',
    });
    const domain = await createTestDomain(user.id, webhook.id, {
      domain: 'test-aliases.com',
      verified: true,
    });

    const alias = await createTestAlias(domain.id, webhook.id, {
      email: '<EMAIL>',
      active: true,
    });

    expect(alias.email).toBe('<EMAIL>');
    expect(alias.domainId).toBe(domain.id);
    expect(alias.webhookId).toBe(webhook.id);
    expect(alias.active).toBe(true);
  });

  test('should list aliases for a user', async () => {
    const user = await createTestUser();
    const webhook1 = await createTestWebhook(user.id, { name: 'Webhook 1' });
    const webhook2 = await createTestWebhook(user.id, { name: 'Webhook 2' });
    const domain = await createTestDomain(user.id, webhook1.id);

    // Create multiple aliases
    await createTestAlias(domain.id, webhook1.id, { email: '<EMAIL>' });
    await createTestAlias(domain.id, webhook2.id, { email: '<EMAIL>' });
    await createTestAlias(domain.id, webhook1.id, { email: '<EMAIL>' });

    // Query aliases with relationships
    const aliases = await prisma.alias.findMany({
      where: {
        domain: {
          userId: user.id,
        },
      },
      include: {
        domain: true,
        webhook: true,
      },
    });

    expect(aliases).toHaveLength(3);
    expect(aliases.map(a => a.email)).toContain('<EMAIL>');
    expect(aliases.map(a => a.email)).toContain('<EMAIL>');
    expect(aliases.map(a => a.email)).toContain('<EMAIL>');

    // Verify relationships
    aliases.forEach(alias => {
      expect(alias.domain.userId).toBe(user.id);
      expect(alias.webhook.userId).toBe(user.id);
    });
  });

  test('should update alias properties', async () => {
    const user = await createTestUser();
    const webhook1 = await createTestWebhook(user.id, { name: 'Original Webhook' });
    const webhook2 = await createTestWebhook(user.id, { name: 'New Webhook' });
    const domain = await createTestDomain(user.id, webhook1.id);

    const alias = await createTestAlias(domain.id, webhook1.id, {
      email: '<EMAIL>',
      active: true,
    });

    // Update webhook and active status
    const updatedAlias = await prisma.alias.update({
      where: { id: alias.id },
      data: {
        webhookId: webhook2.id,
        active: false,
      },
      include: { webhook: true },
    });

    expect(updatedAlias.webhookId).toBe(webhook2.id);
    expect(updatedAlias.webhook.name).toBe('New Webhook');
    expect(updatedAlias.active).toBe(false);
  });

  test('should delete aliases', async () => {
    const user = await createTestUser();
    const webhook = await createTestWebhook(user.id);
    const domain = await createTestDomain(user.id, webhook.id);

    const alias = await createTestAlias(domain.id, webhook.id, {
      email: '<EMAIL>',
    });

    // Verify alias exists
    const existingAlias = await prisma.alias.findUnique({
      where: { id: alias.id },
    });
    expect(existingAlias).toBeTruthy();

    // Delete alias
    await prisma.alias.delete({
      where: { id: alias.id },
    });

    // Verify alias is deleted
    const deletedAlias = await prisma.alias.findUnique({
      where: { id: alias.id },
    });
    expect(deletedAlias).toBeNull();
  });

  test('should enforce unique email addresses within domain', async () => {
    const user = await createTestUser();
    const webhook = await createTestWebhook(user.id);
    const domain = await createTestDomain(user.id, webhook.id);

    // Create first alias
    await createTestAlias(domain.id, webhook.id, {
      email: '<EMAIL>',
    });

    // Attempting to create second alias with same email should fail
    await expect(
      createTestAlias(domain.id, webhook.id, {
        email: '<EMAIL>', // Duplicate email
      })
    ).rejects.toThrow();
  });

  test('should allow same email across different domains', async () => {
    const user = await createTestUser();
    const webhook = await createTestWebhook(user.id);
    
    const domain1 = await createTestDomain(user.id, webhook.id, {
      domain: 'domain1.com',
    });
    const domain2 = await createTestDomain(user.id, webhook.id, {
      domain: 'domain2.com',
    });

    // Create aliases with same local part on different domains
    const alias1 = await createTestAlias(domain1.id, webhook.id, {
      email: '<EMAIL>',
    });
    const alias2 = await createTestAlias(domain2.id, webhook.id, {
      email: '<EMAIL>',
    });

    expect(alias1.email).toBe('<EMAIL>');
    expect(alias2.email).toBe('<EMAIL>');
    expect(alias1.domainId).not.toBe(alias2.domainId);
  });

  test('should prevent creation on unverified domains', async () => {
    const user = await createTestUser();
    const webhook = await createTestWebhook(user.id);
    const unverifiedDomain = await createTestDomain(user.id, webhook.id, {
      domain: 'unverified.com',
      verified: false,
      verificationStatus: 'PENDING',
    });

    // This test depends on business logic in the API layer
    // For now, we'll test that the database allows it but note that
    // the API should prevent this
    const alias = await createTestAlias(unverifiedDomain.id, webhook.id, {
      email: '<EMAIL>',
    });

    expect(alias).toBeTruthy();
    // Note: API layer should prevent this based on domain.verified = false
  });

  test('should handle alias activation/deactivation', async () => {
    const user = await createTestUser();
    const webhook = await createTestWebhook(user.id);
    const domain = await createTestDomain(user.id, webhook.id);

    const alias = await createTestAlias(domain.id, webhook.id, {
      email: '<EMAIL>',
      active: true,
    });

    expect(alias.active).toBe(true);

    // Deactivate alias
    const deactivated = await prisma.alias.update({
      where: { id: alias.id },
      data: { active: false },
    });

    expect(deactivated.active).toBe(false);

    // Reactivate alias
    const reactivated = await prisma.alias.update({
      where: { id: alias.id },
      data: { active: true },
    });

    expect(reactivated.active).toBe(true);
  });

  test('should query aliases by domain', async () => {
    const user = await createTestUser();
    const webhook = await createTestWebhook(user.id);
    
    const domain1 = await createTestDomain(user.id, webhook.id, { domain: 'domain1.com' });
    const domain2 = await createTestDomain(user.id, webhook.id, { domain: 'domain2.com' });

    // Create aliases for both domains
    await createTestAlias(domain1.id, webhook.id, { email: '<EMAIL>' });
    await createTestAlias(domain1.id, webhook.id, { email: '<EMAIL>' });
    await createTestAlias(domain2.id, webhook.id, { email: '<EMAIL>' });

    // Query aliases for domain1 only
    const domain1Aliases = await prisma.alias.findMany({
      where: { domainId: domain1.id },
      include: { domain: true },
    });

    expect(domain1Aliases).toHaveLength(2);
    expect(domain1Aliases[0].domain.domain).toBe('domain1.com');
    expect(domain1Aliases[1].domain.domain).toBe('domain1.com');
  });

  test('should maintain referential integrity on cascade deletes', async () => {
    const user = await createTestUser();
    const webhook = await createTestWebhook(user.id);
    const domain = await createTestDomain(user.id, webhook.id);

    const alias = await createTestAlias(domain.id, webhook.id, {
      email: '<EMAIL>',
    });

    // Verify entities exist
    expect(await prisma.alias.findUnique({ where: { id: alias.id } })).toBeTruthy();
    expect(await prisma.domain.findUnique({ where: { id: domain.id } })).toBeTruthy();

    // Delete domain - should cascade delete alias
    await prisma.domain.delete({ where: { id: domain.id } });

    // Verify cascade deletion
    expect(await prisma.alias.findUnique({ where: { id: alias.id } })).toBeNull();
    expect(await prisma.domain.findUnique({ where: { id: domain.id } })).toBeNull();
  });

  test('should filter active vs inactive aliases', async () => {
    const user = await createTestUser();
    const webhook = await createTestWebhook(user.id);
    const domain = await createTestDomain(user.id, webhook.id);

    // Create mix of active and inactive aliases
    await createTestAlias(domain.id, webhook.id, { 
      email: '<EMAIL>', 
      active: true 
    });
    await createTestAlias(domain.id, webhook.id, { 
      email: '<EMAIL>', 
      active: true 
    });
    await createTestAlias(domain.id, webhook.id, { 
      email: '<EMAIL>', 
      active: false 
    });
    await createTestAlias(domain.id, webhook.id, { 
      email: '<EMAIL>', 
      active: false 
    });

    // Query only active aliases
    const activeAliases = await prisma.alias.findMany({
      where: { 
        domainId: domain.id,
        active: true 
      },
    });

    // Query only inactive aliases
    const inactiveAliases = await prisma.alias.findMany({
      where: { 
        domainId: domain.id,
        active: false 
      },
    });

    expect(activeAliases).toHaveLength(2);
    expect(inactiveAliases).toHaveLength(2);
    expect(activeAliases.every(a => a.active)).toBe(true);
    expect(inactiveAliases.every(a => !a.active)).toBe(true);
  });
});
