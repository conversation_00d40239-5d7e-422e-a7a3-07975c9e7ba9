import { describe, test, expect } from '@jest/globals';
import { 
  prisma, 
  setupTestDatabase, 
  createTestUser, 
  createTestWebhook, 
  createTestDomain,
  createTest<PERSON>lias
} from '../../setup/test-db-setup';

setupTestDatabase();

// Simulate the webhook lookup logic from email processing
function simulateWebhookLookup(domainConfig, emailAddress) {
  // 1. Check for specific alias configuration first
  const alias = domainConfig.aliases.find((alias) =>
    alias.email === emailAddress && alias.active && alias.webhook
  );

  if (alias && alias.webhook) {
    return alias.webhook.url;
  }

  // 2. Fall back to domain's default webhook
  if (domainConfig.active && domainConfig.webhook) {
    return domainConfig.webhook.url;
  }

  return null;
}

describe('Webhook Processing Logic', () => {
  test('should route emails to correct webhooks based on aliases and domain defaults', async () => {
    // Step 1: Create test user
    const testUser = await createTestUser({
      email: '<EMAIL>',
      name: 'Webhook Test User',
    });

    // Step 2: Create webhooks
    const domainWebhook = await createTestWebhook(testUser.id, {
      name: 'Domain Default Webhook',
      url: 'https://api.example.com/domain-webhook',
      description: 'Default webhook for domain emails',
    });

    const supportWebhook = await createTestWebhook(testUser.id, {
      name: 'Support Team Webhook',
      url: 'https://support.example.com/webhook',
      description: 'Webhook for support emails',
    });

    const salesWebhook = await createTestWebhook(testUser.id, {
      name: 'Sales Team Webhook',
      url: 'https://sales.example.com/webhook',
      description: 'Webhook for sales emails',
    });

    // Step 3: Create domain with default webhook
    const testDomain = await createTestDomain(testUser.id, domainWebhook.id, {
      domain: 'webhook-test.com',
      verified: true,
      verificationStatus: 'VERIFIED',
      active: true,
    });

    // Step 4: Create aliases with specific webhooks
    const supportAlias = await createTestAlias(testDomain.id, supportWebhook.id, {
      email: '<EMAIL>',
      active: true,
    });

    const salesAlias = await createTestAlias(testDomain.id, salesWebhook.id, {
      email: '<EMAIL>',
      active: true,
    });

    // Step 5: Get domain config for testing (simulating email processing)
    const domainConfig = await prisma.domain.findUnique({
      where: { domain: 'webhook-test.com' },
      include: { 
        aliases: { 
          include: { webhook: true },
          where: { active: true }
        },
        webhook: true 
      },
    });

    expect(domainConfig).toBeTruthy();
    expect(domainConfig.aliases).toHaveLength(2);

    // Test 1: Support email should use support webhook
    const supportWebhookUrl = simulateWebhookLookup(domainConfig, '<EMAIL>');
    expect(supportWebhookUrl).toBe(supportWebhook.url);

    // Test 2: Sales email should use sales webhook  
    const salesWebhookUrl = simulateWebhookLookup(domainConfig, '<EMAIL>');
    expect(salesWebhookUrl).toBe(salesWebhook.url);

    // Test 3: Random email should use domain default webhook
    const randomWebhookUrl = simulateWebhookLookup(domainConfig, '<EMAIL>');
    expect(randomWebhookUrl).toBe(domainWebhook.url);
  });

  test('should fallback to domain default when alias is inactive', async () => {
    const testUser = await createTestUser();
    const domainWebhook = await createTestWebhook(testUser.id, {
      name: 'Domain Default',
      url: 'https://api.example.com/default',
    });
    const supportWebhook = await createTestWebhook(testUser.id, {
      name: 'Support Webhook',
      url: 'https://support.example.com/webhook',
    });

    const testDomain = await createTestDomain(testUser.id, domainWebhook.id, {
      domain: 'fallback-test.com',
    });

    // Create support alias and then deactivate it
    const supportAlias = await createTestAlias(testDomain.id, supportWebhook.id, {
      email: '<EMAIL>',
      active: true,
    });

    // Now deactivate the alias to test fallback
    await prisma.alias.update({
      where: { id: supportAlias.id },
      data: { active: false }
    });

    // Get domain config with only active aliases
    const domainConfig = await prisma.domain.findUnique({
      where: { domain: 'fallback-test.com' },
      include: { 
        aliases: { 
          include: { webhook: true },
          where: { active: true } // Only active aliases
        },
        webhook: true 
      },
    });

    // Support email should fallback to domain default (alias is inactive)
    const webhookUrl = simulateWebhookLookup(domainConfig, '<EMAIL>');
    expect(webhookUrl).toBe(domainWebhook.url);
    expect(domainConfig.aliases).toHaveLength(0); // No active aliases
  });

  test('should return null for inactive domains', async () => {
    const testUser = await createTestUser();
    const domainWebhook = await createTestWebhook(testUser.id);
    
    const testDomain = await createTestDomain(testUser.id, domainWebhook.id, {
      domain: 'inactive-test.com',
      active: false, // Inactive domain
    });

    const domainConfig = await prisma.domain.findUnique({
      where: { domain: 'inactive-test.com' },
      include: { 
        aliases: { 
          include: { webhook: true },
          where: { active: true }
        },
        webhook: true 
      },
    });

    // Should return null for inactive domain
    const webhookUrl = simulateWebhookLookup(domainConfig, '<EMAIL>');
    expect(webhookUrl).toBeNull();
  });

  test('should handle domain without webhook gracefully', async () => {
    const testUser = await createTestUser();
    const webhook = await createTestWebhook(testUser.id);
    
    // Create domain normally first
    const testDomain = await createTestDomain(testUser.id, webhook.id, {
      domain: 'no-webhook-test.com',
      verified: true,
      verificationStatus: 'VERIFIED',
      active: true,
    });
    
    // Simulate a domain query that might have null webhook
    const domainConfig = {
      ...testDomain,
      webhook: null, // Simulate missing webhook
      aliases: [],
      active: true,
    };

    const webhookUrl = simulateWebhookLookup(domainConfig, '<EMAIL>');
    expect(webhookUrl).toBeNull();
  });

  test('should prioritize alias webhook over domain default', async () => {
    const testUser = await createTestUser();
    
    const domainWebhook = await createTestWebhook(testUser.id, {
      name: 'Domain Default',
      url: 'https://domain.example.com/webhook',
    });
    
    const aliasWebhook = await createTestWebhook(testUser.id, {
      name: 'Specific Alias Webhook',
      url: 'https://specific.example.com/webhook',
    });

    const testDomain = await createTestDomain(testUser.id, domainWebhook.id);
    
    await createTestAlias(testDomain.id, aliasWebhook.id, {
      email: '<EMAIL>',
      active: true,
    });

    const domainConfig = await prisma.domain.findUnique({
      where: { id: testDomain.id },
      include: { 
        aliases: { 
          include: { webhook: true },
          where: { active: true }
        },
        webhook: true 
      },
    });

    // Should use alias webhook, not domain default
    const webhookUrl = simulateWebhookLookup(domainConfig, '<EMAIL>');
    expect(webhookUrl).toBe(aliasWebhook.url);
    expect(webhookUrl).not.toBe(domainWebhook.url);
  });

  test('should handle multiple aliases for same domain correctly', async () => {
    const testUser = await createTestUser();
    const domainWebhook = await createTestWebhook(testUser.id, { url: 'https://domain.example.com/webhook' });
    const supportWebhook = await createTestWebhook(testUser.id, { url: 'https://support.example.com/webhook' });
    const billingWebhook = await createTestWebhook(testUser.id, { url: 'https://billing.example.com/webhook' });

    const testDomain = await createTestDomain(testUser.id, domainWebhook.id, {
      domain: 'multi-alias.com',
    });

    // Create multiple aliases
    await createTestAlias(testDomain.id, supportWebhook.id, { email: '<EMAIL>' });
    await createTestAlias(testDomain.id, billingWebhook.id, { email: '<EMAIL>' });

    const domainConfig = await prisma.domain.findUnique({
      where: { domain: 'multi-alias.com' },
      include: { 
        aliases: { include: { webhook: true }, where: { active: true } },
        webhook: true 
      },
    });

    // Each alias should route to its specific webhook
    expect(simulateWebhookLookup(domainConfig, '<EMAIL>')).toBe(supportWebhook.url);
    expect(simulateWebhookLookup(domainConfig, '<EMAIL>')).toBe(billingWebhook.url);
    expect(simulateWebhookLookup(domainConfig, '<EMAIL>')).toBe(domainWebhook.url);
  });

  test('should properly simulate complete email processing workflow', async () => {
    // This test replicates the exact patterns from the legacy script
    const testUser = await createTestUser({
      email: '<EMAIL>',
      name: 'Complete Test User',
      verified: true,
    });

    // Create three webhooks like in legacy test
    const domainWebhook = await createTestWebhook(testUser.id, {
      name: 'Domain Default Webhook',
      url: 'https://api.example.com/domain-webhook',
      description: 'Default webhook for domain emails',
    });

    const supportWebhook = await createTestWebhook(testUser.id, {
      name: 'Support Team Webhook',
      url: 'https://support.example.com/webhook',
      description: 'Webhook for support emails',
    });

    const salesWebhook = await createTestWebhook(testUser.id, {
      name: 'Sales Team Webhook', 
      url: 'https://sales.example.com/webhook',
      description: 'Webhook for sales emails',
    });

    // Create domain exactly like legacy test
    const testDomain = await createTestDomain(testUser.id, domainWebhook.id, {
      domain: 'complete-webhook-test.com',
      verified: true,
      verificationStatus: 'VERIFIED',
      active: true,
    });

    // Create aliases exactly like legacy test
    const supportAlias = await createTestAlias(testDomain.id, supportWebhook.id, {
      email: '<EMAIL>',
      active: true,
    });

    const salesAlias = await createTestAlias(testDomain.id, salesWebhook.id, {
      email: '<EMAIL>',
      active: true,
    });

    // Test webhook lookup logic exactly like legacy test
    const domainConfigForTest = await prisma.domain.findUnique({
      where: { domain: 'complete-webhook-test.com' },
      include: { 
        aliases: { 
          include: { webhook: true },
          where: { active: true }
        },
        webhook: true 
      },
    });

    // Test 1: Support email routing
    const supportResult = simulateWebhookLookup(domainConfigForTest, '<EMAIL>');
    expect(supportResult).toBe(supportWebhook.url);

    // Test 2: Sales email routing
    const salesResult = simulateWebhookLookup(domainConfigForTest, '<EMAIL>');
    expect(salesResult).toBe(salesWebhook.url);

    // Test 3: Random email fallback
    const randomResult = simulateWebhookLookup(domainConfigForTest, '<EMAIL>');
    expect(randomResult).toBe(domainWebhook.url);

    // Test 4: Inactive alias fallback (like in legacy test Step 6)
    await prisma.alias.update({
      where: { id: supportAlias.id },
      data: { active: false }
    });

    const domainConfigWithInactiveSupport = await prisma.domain.findUnique({
      where: { domain: 'complete-webhook-test.com' },
      include: { 
        aliases: { 
          include: { webhook: true },
          where: { active: true }
        },
        webhook: true 
      },
    });

    const inactiveResult = simulateWebhookLookup(domainConfigWithInactiveSupport, '<EMAIL>');
    expect(inactiveResult).toBe(domainWebhook.url); // Should fallback to domain default

    // Verify test completeness
    expect(domainConfigWithInactiveSupport.aliases).toHaveLength(1); // Only sales alias active
    expect(domainConfigWithInactiveSupport.webhook.url).toBe(domainWebhook.url);
  });
});
